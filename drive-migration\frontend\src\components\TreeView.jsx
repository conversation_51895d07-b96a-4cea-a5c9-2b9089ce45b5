import React, { useState } from 'react';

const TreeView = ({ tree, selectedFiles, onFileSelect, onSelectAll }) => {
    const [expandedFolders, setExpandedFolders] = useState(new Set());

    const toggleFolder = (folderId) => {
        const newExpanded = new Set(expandedFolders);
        if (newExpanded.has(folderId)) {
            newExpanded.delete(folderId);
        } else {
            newExpanded.add(folderId);
        }
        setExpandedFolders(newExpanded);
    };

    const isFileSelected = (fileId) => {
        return selectedFiles.some(f => f.id === fileId);
    };

    const getFileIcon = (mimeType, type) => {
        if (type === 'folder') return '📁';

        if (mimeType?.startsWith('image/')) return '🖼️';
        if (mimeType?.startsWith('video/')) return '🎥';
        if (mimeType?.startsWith('audio/')) return '🎵';
        if (mimeType?.includes('pdf')) return '📄';
        if (mimeType?.includes('document') || mimeType?.includes('word')) return '📝';
        if (mimeType?.includes('spreadsheet') || mimeType?.includes('excel')) return '📊';
        if (mimeType?.includes('presentation') || mimeType?.includes('powerpoint')) return '📈';
        if (mimeType?.includes('zip') || mimeType?.includes('archive')) return '📦';

        return '📄';
    };

    const formatFileSize = (bytes) => {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const renderTreeNode = (node, depth = 0) => {
        const isFolder = node.type === 'folder';
        const isExpanded = expandedFolders.has(node.id);
        const hasChildren = node.children && node.children.length > 0;
        const isSelected = !isFolder && isFileSelected(node.id);

        return (
            <div key={node.id} className="tree-node">
                <div
                    className={`tree-item ${isSelected ? 'selected' : ''}`}
                    style={{ paddingLeft: `${depth * 20 + 10}px` }}
                >
                    {/* Expand/Collapse button for folders */}
                    {isFolder && (
                        <button
                            className={`tree-toggle ${hasChildren ? '' : 'empty'}`}
                            onClick={() => toggleFolder(node.id)}
                            disabled={!hasChildren}
                        >
                            {hasChildren ? (isExpanded ? '▼' : '▶') : '○'}
                        </button>
                    )}

                    {/* Checkbox for files */}
                    {!isFolder && (
                        <label className="tree-checkbox">
                            <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => onFileSelect(node, e.target.checked)}
                            />
                            <span className="checkbox-custom"></span>
                        </label>
                    )}

                    {/* Icon */}
                    <span className="tree-icon">
                        {getFileIcon(node.mime_type, node.type)}
                    </span>

                    {/* Name */}
                    <span className="tree-name" title={node.name}>
                        {node.name}
                    </span>

                    {/* File info */}
                    <div className="tree-info">
                        {isFolder ? (
                            <span className="folder-stats">
                                {node.fileCount > 0 && `${node.fileCount} files`}
                                {node.fileCount > 0 && node.folderCount > 0 && ', '}
                                {node.folderCount > 0 && `${node.folderCount} folders`}
                                {node.totalSize > 0 && ` (${formatFileSize(node.totalSize)})`}
                            </span>
                        ) : (
                            <>
                                <span className="file-size">{formatFileSize(node.size)}</span>
                                {node.modified_time && (
                                    <span className="file-date">
                                        {new Date(node.modified_time).toLocaleDateString()}
                                    </span>
                                )}
                            </>
                        )}
                    </div>
                </div>

                {/* Children */}
                {isFolder && isExpanded && hasChildren && (
                    <div className="tree-children">
                        {node.children
                            .sort((a, b) => {
                                // Folders first, then files, both alphabetically (case-insensitive)
                                if (a.type !== b.type) {
                                    return a.type === 'folder' ? -1 : 1;
                                }
                                return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
                            })
                            .map(child => renderTreeNode(child, depth + 1))
                        }
                    </div>
                )}
            </div>
        );
    };

    const getAllFiles = (nodes) => {
        let files = [];
        nodes.forEach(node => {
            if (node.type === 'file') {
                files.push(node);
            }
            if (node.children) {
                files = files.concat(getAllFiles(node.children));
            }
        });
        return files;
    };

    const allFiles = getAllFiles(tree);
    const selectedCount = selectedFiles.length;
    const totalCount = allFiles.length;

    return (
        <div className="tree-view">
            <div className="tree-header">
                <div className="tree-controls">
                    <label className="select-all-checkbox">
                        <input
                            type="checkbox"
                            checked={selectedCount === totalCount && totalCount > 0}
                            onChange={(e) => onSelectAll(e.target.checked ? allFiles : [])}
                        />
                        <span className="checkbox-custom"></span>
                        <span>Select All ({totalCount} files)</span>
                    </label>
                </div>

                <div className="tree-stats">
                    <span>Selected: {selectedCount} / {totalCount}</span>
                </div>
            </div>

            <div className="tree-content">
                {tree
                    .sort((a, b) => {
                        // Folders first, then files, both alphabetically (case-insensitive)
                        if (a.type !== b.type) {
                            return a.type === 'folder' ? -1 : 1;
                        }
                        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
                    })
                    .map(node => renderTreeNode(node))
                }
            </div>
        </div>
    );
};

export default TreeView;
