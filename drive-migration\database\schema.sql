-- Drive-to-Lark Migration Database Schema
-- T<PERSON><PERSON> các bảng cần thiết cho hệ thống migration

-- Bảng mapping người dùng Google -> Lark
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email_google TEXT UNIQUE NOT NULL,
  lark_userid TEXT,
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng scan sessions cho Drive scanning
CREATE TABLE scan_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_email TEXT NOT NULL,
  scan_type TEXT NOT NULL CHECK (scan_type IN ('full_drive', 'folder_specific', 'selective')),
  scan_options JSONB, -- Lưu options như maxDepth, filterMimeTypes, etc.
  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  scanned_files INTEGER DEFAULT 0,
  current_depth INTEGER DEFAULT 0,
  folders_processed INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  scan_duration INTEGER, -- milliseconds
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT
);

-- Bảng lưu trữ files đã scan
CREATE TABLE scanned_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_session_id UUID NOT NULL REFERENCES scan_sessions(id) ON DELETE CASCADE,
  file_id TEXT NOT NULL, -- Google Drive file ID
  name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT DEFAULT 0,
  full_path TEXT NOT NULL, -- Đường dẫn đầy đủ
  depth INTEGER NOT NULL DEFAULT 0,
  parents TEXT[], -- Array of parent folder IDs
  created_time TIMESTAMPTZ,
  modified_time TIMESTAMPTZ,
  owners JSONB, -- Google Drive owners info
  permissions JSONB, -- Google Drive permissions
  web_view_link TEXT,
  metadata JSONB, -- Additional metadata (iconLink, thumbnailLink, etc.)
  is_selected BOOLEAN DEFAULT FALSE, -- Được chọn để migrate
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng tác vụ migration
CREATE TABLE migration_tasks (
  id UUID PRIMARY KEY, -- Custom migration ID
  owner_email TEXT NOT NULL,
  scan_session_id UUID REFERENCES scan_sessions(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'completed_with_errors', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  processed_files INTEGER DEFAULT 0,
  successful_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0, -- Tổng kích thước file (bytes)
  processed_size BIGINT DEFAULT 0, -- Đã xử lý (bytes)
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  options JSONB, -- Migration options (mapPermissions, targetRootFolder, etc.)
  checkpoint_data JSONB -- Checkpoint data for resume capability
);

-- Bảng chi tiết từng file migration
CREATE TABLE migration_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  migration_task_id UUID NOT NULL REFERENCES migration_tasks(id) ON DELETE CASCADE,
  google_file_id TEXT NOT NULL, -- Google Drive file ID
  google_file_name TEXT NOT NULL,
  google_file_path TEXT NOT NULL, -- Đường dẫn đầy đủ trong Drive
  google_file_size BIGINT DEFAULT 0,
  lark_file_token TEXT, -- Lark Drive file token sau khi upload
  lark_file_name TEXT, -- File name in Lark
  lark_folder_token TEXT, -- Lark folder token
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'uploading', 'completed', 'failed', 'skipped')),
  error_message TEXT,
  download_time INTEGER, -- Download time in milliseconds
  upload_time INTEGER, -- Upload time in milliseconds
  retries INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB -- Lưu metadata bổ sung (permissions, checksums, etc.)
);

-- Bảng mapping quyền truy cập
CREATE TABLE permission_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  migration_item_id UUID NOT NULL REFERENCES migration_items(id) ON DELETE CASCADE,
  google_email TEXT NOT NULL,
  google_role TEXT NOT NULL, -- owner, writer, reader, commenter
  lark_user_id TEXT, -- Mapped Lark user ID
  lark_role TEXT, -- full_access, edit, view, comment
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng log hệ thống
CREATE TABLE migration_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES migration_tasks(id) ON DELETE CASCADE,
  item_id UUID REFERENCES migration_items(id) ON DELETE CASCADE,
  level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'debug')),
  message TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes để tối ưu performance
CREATE INDEX idx_migration_tasks_owner_email ON migration_tasks(owner_email);
CREATE INDEX idx_migration_tasks_status ON migration_tasks(status);
CREATE INDEX idx_migration_tasks_created_at ON migration_tasks(created_at);

CREATE INDEX idx_migration_items_task_id ON migration_items(migration_task_id);
CREATE INDEX idx_migration_items_status ON migration_items(status);
CREATE INDEX idx_migration_items_src_file_id ON migration_items(google_file_id);

CREATE INDEX idx_permission_mappings_migration_item_id ON permission_mappings(migration_item_id);
CREATE INDEX idx_permission_mappings_google_email ON permission_mappings(google_email);
CREATE INDEX idx_permission_mappings_mapped ON permission_mappings(mapped);

CREATE INDEX idx_migration_logs_task_id ON migration_logs(task_id);
CREATE INDEX idx_migration_logs_level ON migration_logs(level);
CREATE INDEX idx_migration_logs_created_at ON migration_logs(created_at);

CREATE INDEX idx_users_email_google ON users(email_google);
CREATE INDEX idx_users_mapped ON users(mapped);

-- Triggers để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_migration_tasks_updated_at BEFORE UPDATE ON migration_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_migration_items_updated_at BEFORE UPDATE ON migration_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_permission_mappings_updated_at BEFORE UPDATE ON permission_mappings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views để dễ dàng query thống kê
CREATE VIEW migration_task_stats AS
SELECT 
    t.id,
    t.owner_email,
    t.status,
    t.processed_files,
    t.total_files,
    t.successful_files,
    t.failed_files,
    CASE 
        WHEN t.total_files > 0 THEN ROUND((t.successful_files::NUMERIC / t.total_files::NUMERIC) * 100, 2)
        ELSE 0 
    END as completion_percentage,
    t.total_size,
    t.processed_size,
    CASE 
        WHEN t.total_size > 0 THEN ROUND((t.processed_size::NUMERIC / t.total_size::NUMERIC) * 100, 2)
        ELSE 0 
    END as transfer_percentage,
    t.started_at,
    t.completed_at,
    CASE 
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - t.started_at))
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (t.completed_at - t.started_at))
        ELSE NULL 
    END as duration_seconds
FROM migration_tasks t;

-- Indexes for scan_sessions
CREATE INDEX idx_scan_sessions_user_email ON scan_sessions(user_email);
CREATE INDEX idx_scan_sessions_status ON scan_sessions(status);
CREATE INDEX idx_scan_sessions_started_at ON scan_sessions(started_at);

-- Indexes for scanned_files
CREATE INDEX idx_scanned_files_session_id ON scanned_files(scan_session_id);
CREATE INDEX idx_scanned_files_file_id ON scanned_files(file_id);
CREATE INDEX idx_scanned_files_mime_type ON scanned_files(mime_type);
CREATE INDEX idx_scanned_files_full_path ON scanned_files(full_path);
CREATE INDEX idx_scanned_files_is_selected ON scanned_files(is_selected);
CREATE INDEX idx_scanned_files_depth ON scanned_files(depth);

-- RLS (Row Level Security) policies
ALTER TABLE migration_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE migration_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE scan_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE scanned_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE migration_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own migration tasks
CREATE POLICY "Users can view own migration tasks" ON migration_tasks
    FOR SELECT USING (owner_email = auth.email());

CREATE POLICY "Users can insert own migration tasks" ON migration_tasks
    FOR INSERT WITH CHECK (owner_email = auth.email());

CREATE POLICY "Users can update own migration tasks" ON migration_tasks
    FOR UPDATE USING (owner_email = auth.email());

-- Policy: Users can only see migration items of their tasks
CREATE POLICY "Users can view own migration items" ON migration_items
    FOR SELECT USING (
        id IN (SELECT id FROM migration_tasks WHERE owner_email = auth.email())
    );

-- Policy: Service role can access all data
CREATE POLICY "Service role full access migration_tasks" ON migration_tasks
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access migration_items" ON migration_items
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access permission_mappings" ON permission_mappings
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access migration_logs" ON migration_logs
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access users" ON users
    FOR ALL USING (auth.role() = 'service_role');

-- Realtime subscriptions
ALTER PUBLICATION supabase_realtime ADD TABLE migration_tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_items;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_logs;

-- Function để lấy migration statistics
CREATE OR REPLACE FUNCTION get_migration_stats(task_id TEXT)
RETURNS TABLE (
    total_files INTEGER,
    completed_files INTEGER,
    failed_files INTEGER,
    in_progress_files INTEGER,
    pending_files INTEGER,
    success_rate DECIMAL,
    total_size BIGINT,
    processed_size BIGINT,
    avg_processing_time DECIMAL,
    total_processing_time BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_files,
        COUNT(CASE WHEN mi.status = 'completed' THEN 1 END)::INTEGER as completed_files,
        COUNT(CASE WHEN mi.status = 'failed' THEN 1 END)::INTEGER as failed_files,
        COUNT(CASE WHEN mi.status IN ('downloading', 'uploading') THEN 1 END)::INTEGER as in_progress_files,
        COUNT(CASE WHEN mi.status = 'pending' THEN 1 END)::INTEGER as pending_files,
        CASE
            WHEN COUNT(*) > 0 THEN
                ROUND((COUNT(CASE WHEN mi.status = 'completed' THEN 1 END)::DECIMAL / COUNT(*)) * 100, 2)
            ELSE 0
        END as success_rate,
        COALESCE(SUM(mi.google_file_size), 0) as total_size,
        COALESCE(SUM(CASE WHEN mi.status = 'completed' THEN mi.google_file_size ELSE 0 END), 0) as processed_size,
        CASE
            WHEN COUNT(CASE WHEN mi.status = 'completed' THEN 1 END) > 0 THEN
                ROUND(AVG(CASE WHEN mi.status = 'completed' THEN (COALESCE(mi.download_time, 0) + COALESCE(mi.upload_time, 0)) END), 2)
            ELSE 0
        END as avg_processing_time,
        COALESCE(SUM(CASE WHEN mi.status = 'completed' THEN (COALESCE(mi.download_time, 0) + COALESCE(mi.upload_time, 0)) ELSE 0 END), 0) as total_processing_time
    FROM migration_items mi
    WHERE mi.migration_task_id = task_id;
END;
$$ LANGUAGE plpgsql;
