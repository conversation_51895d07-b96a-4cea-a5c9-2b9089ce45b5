// API utility functions for better error handling

/**
 * Enhanced fetch wrapper with better error handling
 * @param {string} url - API endpoint URL
 * @param {object} options - Fetch options
 * @returns {Promise<object>} - Response data or throws enhanced error
 */
export const apiCall = async (url, options = {}) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    // Check if response is ok
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText };
      }

      // Create enhanced error with Vietnamese messages
      const vietnameseMessage = getVietnameseErrorMessage(response.status, errorData.message);
      const error = new Error(vietnameseMessage);
      error.status = response.status;
      error.statusText = response.statusText;
      error.originalMessage = errorData.message;
      error.response = {
        status: response.status,
        statusText: response.statusText,
        data: errorData
      };

      throw error;
    }

    // Try to parse JSON response
    try {
      return await response.json();
    } catch (e) {
      // If response is not JSON, return text
      return await response.text();
    }
  } catch (error) {
    // Network errors or other fetch errors
    if (!error.status) {
      error.message = `Lỗi kết nối: ${error.message}`;
    }
    throw error;
  }
};

/**
 * Get Vietnamese error message based on status code and original message
 * @param {number} status - HTTP status code
 * @param {string} originalMessage - Original error message
 * @returns {string} - Vietnamese error message
 */
const getVietnameseErrorMessage = (status, originalMessage) => {
  const statusMessages = {
    400: 'Yêu cầu không hợp lệ',
    401: 'Không có quyền truy cập',
    403: 'Bị cấm truy cập',
    404: 'Không tìm thấy tài nguyên',
    408: 'Hết thời gian chờ',
    429: 'Quá nhiều yêu cầu, vui lòng thử lại sau',
    500: 'Lỗi máy chủ nội bộ',
    502: 'Lỗi gateway',
    503: 'Dịch vụ không khả dụng',
    504: 'Hết thời gian chờ gateway'
  };

  const baseMessage = statusMessages[status] || `Lỗi HTTP ${status}`;

  // If original message contains useful info, append it
  if (originalMessage && originalMessage !== 'Internal Server Error' && originalMessage !== 'Bad Request') {
    return `${baseMessage}: ${originalMessage}`;
  }

  return baseMessage;
};

/**
 * GET request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiGet = (url, options = {}) => {
  return apiCall(url, { method: 'GET', ...options });
};

/**
 * POST request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} data - Request body data
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiPost = (url, data = null, options = {}) => {
  return apiCall(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options
  });
};

/**
 * PUT request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} data - Request body data
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiPut = (url, data = null, options = {}) => {
  return apiCall(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options
  });
};

/**
 * DELETE request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiDelete = (url, options = {}) => {
  return apiCall(url, { method: 'DELETE', ...options });
};

/**
 * Format error for display
 * @param {Error} error - Error object
 * @returns {object} - Formatted error info
 */
export const formatError = (error) => {
  if (typeof error === 'string') {
    return { message: error, details: null, code: null };
  }

  if (error instanceof Error) {
    // Build detailed error info
    let details = [];

    if (error.originalMessage && error.originalMessage !== error.message) {
      details.push(`Thông báo gốc: ${error.originalMessage}`);
    }

    if (error.status) {
      details.push(`Mã lỗi HTTP: ${error.status}`);
    }

    if (error.response?.data) {
      details.push(`Chi tiết API: ${JSON.stringify(error.response.data, null, 2)}`);
    }

    // Add stack trace for development
    if (process.env.NODE_ENV === 'development' && error.stack) {
      details.push(`Stack trace:\n${error.stack}`);
    }

    return {
      message: error.message,
      details: details.length > 0 ? details.join('\n\n') : null,
      code: error.status || error.code || null
    };
  }

  // Handle API error responses
  if (error.response) {
    return {
      message: error.response.data?.message || error.response.statusText || 'Lỗi API',
      details: error.response.data?.details || `HTTP ${error.response.status}`,
      code: error.response.status
    };
  }

  return {
    message: error.message || 'Lỗi không xác định',
    details: JSON.stringify(error, null, 2),
    code: null
  };
};

/**
 * Create a retry function for API calls
 * @param {Function} apiFunction - API function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in ms
 * @returns {Function} - Retry wrapper function
 */
export const createRetryWrapper = (apiFunction, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction(...args);
      } catch (error) {
        lastError = error;

        // Don't retry on client errors (4xx)
        if (error.status >= 400 && error.status < 500) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)));
      }
    }

    throw lastError;
  };
};
