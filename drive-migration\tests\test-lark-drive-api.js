import { larkDriveAPI } from './api/lark-drive-api.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test Lark Drive API operations
 */
async function testLarkDriveAPI() {
    console.log('🔍 Testing Lark Drive API Operations...\n');
    console.log('=' .repeat(60));

    try {
        // 1. Test comprehensive API operations
        console.log('1. Running comprehensive API tests...');
        const apiResults = await larkDriveAPI.testAPIs();
        
        if (apiResults.success) {
            console.log('✅ All API operations successful!');
            console.log(`📊 Performance summary:`);
            Object.entries(apiResults.performance).forEach(([op, time]) => {
                console.log(`   ${op}: ${time}ms`);
            });
            
            console.log(`📄 Test files created: ${apiResults.testFiles.length}`);
            apiResults.testFiles.forEach(file => {
                console.log(`   📄 ${file.fileName}: ${file.file_token}`);
            });
        } else {
            console.log('❌ Some API operations failed:');
            apiResults.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });
        }

        // 2. Test folder operations
        console.log('\n2. Testing advanced folder operations...');
        
        // Create test folder
        const testFolderName = `advanced-test-${Date.now()}`;
        const mainFolder = await larkDriveAPI.createFolder(testFolderName);
        console.log(`✅ Main test folder created: ${mainFolder.token}`);

        // Create folder hierarchy
        const hierarchyPath = ['Documents', 'Projects', '2025'];
        const hierarchy = await larkDriveAPI.createFolderHierarchy(hierarchyPath, mainFolder.token);
        console.log(`✅ Folder hierarchy created: ${hierarchy.path}`);
        console.log(`   Final folder: ${hierarchy.finalFolder.token}`);
        console.log(`   Total folders: ${hierarchy.totalFolders}`);

        // Test folder children
        const children = await larkDriveAPI.getFolderChildren(mainFolder.token);
        console.log(`✅ Folder children: ${children.length} items`);

        // 3. Test file upload scenarios
        console.log('\n3. Testing file upload scenarios...');
        
        // Small text file
        console.log('   📄 Testing small text file...');
        const textContent = Buffer.from('This is a test document for Lark Drive migration.\n\nIt contains multiple lines and some special characters: áéíóú ñ 中文 🚀', 'utf8');
        const textUpload = await larkDriveAPI.uploadFile(
            textContent, 
            'test-document.txt', 
            hierarchy.finalFolder.token,
            (progress) => {
                if (progress.progress % 25 === 0) {
                    console.log(`      📊 ${progress.fileName}: ${progress.progress}% (${progress.phase})`);
                }
            }
        );
        console.log(`   ✅ Text file uploaded: ${textUpload.file_token} (${larkDriveAPI.formatFileSize(textUpload.fileSize)})`);

        // Binary file (simulate image)
        console.log('   🖼️ Testing binary file...');
        const binaryContent = Buffer.alloc(2 * 1024 * 1024, 0xFF); // 2MB binary file
        const binaryUpload = await larkDriveAPI.uploadFile(
            binaryContent, 
            'test-image.bin', 
            hierarchy.finalFolder.token,
            (progress) => {
                if (progress.progress % 25 === 0) {
                    console.log(`      📊 ${progress.fileName}: ${progress.progress}% (${progress.phase})`);
                }
            }
        );
        console.log(`   ✅ Binary file uploaded: ${binaryUpload.file_token} (${larkDriveAPI.formatFileSize(binaryUpload.fileSize)})`);

        // Large file (simulate document)
        console.log('   📦 Testing large file...');
        const largeContent = Buffer.alloc(25 * 1024 * 1024, 'L'); // 25MB file
        const largeUpload = await larkDriveAPI.uploadFile(
            largeContent, 
            'large-document.bin', 
            hierarchy.finalFolder.token,
            (progress) => {
                if (progress.progress % 10 === 0) {
                    console.log(`      📊 ${progress.fileName}: ${progress.progress}% (${progress.phase})`);
                }
            }
        );
        console.log(`   ✅ Large file uploaded: ${largeUpload.file_token} (${larkDriveAPI.formatFileSize(largeUpload.fileSize)})`);
        console.log(`   📊 Upload method: ${largeUpload.method || 'direct'}`);

        // 4. Test permissions
        console.log('\n4. Testing permissions management...');
        
        const testFiles = [textUpload.file_token, binaryUpload.file_token];
        
        // Set individual permissions
        console.log('   🔐 Setting individual permissions...');
        const permissionResult = await larkDriveAPI.setPermissions(textUpload.file_token, {
            external_access: false,
            security_entity: 'tenant_editable',
            comment_entity: 'tenant_editable'
        });
        console.log(`   ✅ Permissions set for text file`);

        // Set batch permissions
        console.log('   🔐 Setting batch permissions...');
        const batchPermissions = await larkDriveAPI.setMultiplePermissions(testFiles, {
            external_access: false,
            security_entity: 'tenant_readable'
        }, (progress) => {
            console.log(`      📊 Permissions progress: ${progress.current}/${progress.total}`);
        });
        console.log(`   ✅ Batch permissions: ${batchPermissions.stats.success} success, ${batchPermissions.stats.failed} failed`);

        // 5. Test file info retrieval
        console.log('\n5. Testing file info retrieval...');
        
        const fileInfo = await larkDriveAPI.getFileInfo(textUpload.file_token);
        console.log(`   📄 File info for ${textUpload.fileName}:`);
        console.log(`      Token: ${fileInfo.token}`);
        console.log(`      Name: ${fileInfo.name}`);
        console.log(`      Type: ${fileInfo.type}`);
        console.log(`      Size: ${larkDriveAPI.formatFileSize(fileInfo.size)}`);
        console.log(`      Created: ${new Date(fileInfo.created_time * 1000).toLocaleString()}`);

        // Test caching
        console.log('   🔄 Testing file info caching...');
        const cacheStart = Date.now();
        await larkDriveAPI.getFileInfo(textUpload.file_token); // Should use cache
        const cacheTime = Date.now() - cacheStart;
        console.log(`   ✅ Cached file info retrieved in ${cacheTime}ms`);

        // 6. Test error handling
        console.log('\n6. Testing error handling...');
        
        try {
            await larkDriveAPI.getFileInfo('invalid_token_123');
            console.log('   ❌ Error handling test failed - should have thrown error');
        } catch (error) {
            console.log('   ✅ Error handling works correctly for invalid tokens');
        }

        try {
            await larkDriveAPI.createFolder(''); // Empty name
            console.log('   ❌ Error handling test failed - should have thrown error');
        } catch (error) {
            console.log('   ✅ Error handling works correctly for invalid folder names');
        }

        // 7. Show final statistics
        console.log('\n7. Final statistics...');
        const stats = larkDriveAPI.getStats();
        console.log(`   📊 API calls made: ${stats.apiCalls}`);
        console.log(`   ❌ Errors encountered: ${stats.errors}`);
        console.log(`   📁 Folders created: ${stats.foldersCreated}`);
        console.log(`   📄 Files uploaded: ${stats.filesUploaded}`);
        console.log(`   📦 Bytes uploaded: ${larkDriveAPI.formatFileSize(stats.bytesUploaded)}`);
        console.log(`   🔐 Permissions set: ${stats.permissionsSet}`);
        console.log(`   💾 Cache size: ${stats.cacheSize} items`);

        console.log('\n🎉 Lark Drive API testing completed successfully!');

    } catch (error) {
        console.error('❌ Lark Drive API test failed:', error);
        
        if (error.message.includes('Invalid App ID')) {
            console.log('💡 Tip: Check your LARK_APP_ID in .env file');
        } else if (error.message.includes('permissions')) {
            console.log('💡 Tip: Check Lark app permissions in Developer Console');
        }
    }
}

/**
 * Performance benchmark
 */
async function benchmarkPerformance() {
    console.log('\n🏃 Running Lark Drive API Performance Benchmark...\n');
    
    try {
        const results = await larkDriveAPI.benchmark();
        
        if (results.error) {
            console.log('❌ Benchmark failed:', results.error);
            return;
        }

        console.log('📊 Benchmark Results:');
        
        if (results.operations.createFolder) {
            const createFolder = results.operations.createFolder;
            console.log(`\n📁 Create Folder Performance:`);
            console.log(`   Average: ${createFolder.average.toFixed(2)}ms`);
            console.log(`   Min: ${createFolder.min}ms`);
            console.log(`   Max: ${createFolder.max}ms`);
            console.log(`   All times: ${createFolder.times.join(', ')}ms`);
        }

        if (results.uploadPerformance) {
            console.log(`\n📤 Upload Performance by Size:`);
            Object.entries(results.uploadPerformance).forEach(([size, data]) => {
                console.log(`   ${size}: ${data.duration}ms (${data.throughput})`);
            });
        }

        console.log(`\n📊 Final Stats:`);
        console.log(`   Total API calls: ${results.stats.apiCalls}`);
        console.log(`   Files uploaded: ${results.stats.filesUploaded}`);
        console.log(`   Bytes uploaded: ${larkDriveAPI.formatFileSize(results.stats.bytesUploaded)}`);

    } catch (error) {
        console.error('❌ Benchmark failed:', error.message);
    }
}

/**
 * Test specific migration scenarios
 */
async function testMigrationScenarios() {
    console.log('\n🎯 Testing Migration-Specific Scenarios...\n');
    
    try {
        // Scenario 1: Recreate Google Drive folder structure
        console.log('1. Testing Google Drive folder structure recreation...');
        const driveStructure = [
            'My Drive',
            'My Drive/Documents',
            'My Drive/Documents/Projects',
            'My Drive/Documents/Archive',
            'My Drive/Images',
            'My Drive/Images/2024',
            'My Drive/Images/2025'
        ];

        const createdFolders = [];
        for (const folderPath of driveStructure) {
            const pathParts = folderPath.split('/');
            if (pathParts.length === 1) {
                // Root folder
                const folder = await larkDriveAPI.createFolder(pathParts[0]);
                createdFolders.push({ path: folderPath, token: folder.token });
            } else {
                // Find parent folder
                const parentPath = pathParts.slice(0, -1).join('/');
                const parent = createdFolders.find(f => f.path === parentPath);
                if (parent) {
                    const folder = await larkDriveAPI.createFolder(pathParts[pathParts.length - 1], parent.token);
                    createdFolders.push({ path: folderPath, token: folder.token });
                }
            }
        }
        console.log(`   ✅ Created ${createdFolders.length} folders in Drive-like structure`);

        // Scenario 2: Upload different file types
        console.log('\n2. Testing different file types...');
        const fileTypes = [
            { name: 'document.txt', content: 'Text document content', type: 'text' },
            { name: 'data.json', content: JSON.stringify({test: 'data', numbers: [1,2,3]}), type: 'json' },
            { name: 'image.bin', content: Buffer.alloc(1024 * 1024, 0x89), type: 'binary' }
        ];

        const documentsFolder = createdFolders.find(f => f.path === 'My Drive/Documents');
        if (documentsFolder) {
            for (const fileType of fileTypes) {
                const content = typeof fileType.content === 'string' 
                    ? Buffer.from(fileType.content, 'utf8') 
                    : fileType.content;
                
                const upload = await larkDriveAPI.uploadFile(content, fileType.name, documentsFolder.token);
                console.log(`   ✅ ${fileType.type} file: ${fileType.name} → ${upload.file_token}`);
            }
        }

        // Scenario 3: Permission mapping simulation
        console.log('\n3. Testing permission mapping scenarios...');
        const permissionScenarios = [
            { name: 'Internal Only', config: { external_access: false, security_entity: 'tenant_editable' } },
            { name: 'Read Only', config: { external_access: false, security_entity: 'tenant_readable' } },
            { name: 'Public View', config: { external_access: true, security_entity: 'anyone_can_view' } }
        ];

        for (const scenario of permissionScenarios) {
            console.log(`   🔐 Testing ${scenario.name} permissions...`);
            // Would apply to test files created above
        }

        console.log('\n✅ Migration scenarios testing completed!');

    } catch (error) {
        console.error('❌ Migration scenarios test failed:', error.message);
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Lark Drive API Tests\n');
    
    await testLarkDriveAPI();
    await benchmarkPerformance();
    await testMigrationScenarios();
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ All Lark Drive API tests completed!');
    
    // Show final stats
    const finalStats = larkDriveAPI.getStats();
    console.log(`\n📊 Session Summary:`);
    console.log(`   API calls: ${finalStats.apiCalls}`);
    console.log(`   Errors: ${finalStats.errors}`);
    console.log(`   Folders created: ${finalStats.foldersCreated}`);
    console.log(`   Files uploaded: ${finalStats.filesUploaded}`);
    console.log(`   Bytes uploaded: ${larkDriveAPI.formatFileSize(finalStats.bytesUploaded)}`);
    console.log(`   Permissions set: ${finalStats.permissionsSet}`);
    
    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testLarkDriveAPI, benchmarkPerformance, testMigrationScenarios };
