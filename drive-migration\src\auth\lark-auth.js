import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Lark Authentication và Token Management
 * Enhanced version với comprehensive API support và error handling
 */
export class LarkAuth {
    constructor() {
        this.appId = process.env.LARK_APP_ID;
        this.appSecret = process.env.LARK_APP_SECRET;
        this.baseUrl = 'https://open.larksuite.com/open-apis';

        // Enhanced caching
        this.cachedToken = null;
        this.tokenExpiry = null;
        this.clientCache = new Map();

        // API endpoints
        this.endpoints = {
            tenantToken: '/auth/v3/tenant_access_token/internal',
            appInfo: '/application/v6/applications/self',
            userInfo: '/contact/v3/users/me',
            driveInfo: '/drive/v1/metas/batch_query',
            uploadPrepare: '/drive/v1/files/upload_prepare',
            uploadPart: '/drive/v1/files/upload_part',
            uploadFinish: '/drive/v1/files/upload_finish'
        };

        // Rate limiting
        this.rateLimiter = {
            requests: 0,
            resetTime: Date.now() + 60000, // Reset every minute
            maxRequests: 100 // Max 100 requests per minute
        };

        if (!this.appId || !this.appSecret) {
            throw new Error('Missing required Lark App credentials (LARK_APP_ID, LARK_APP_SECRET)');
        }

        console.log('✅ Lark Auth initialized with App ID:', this.appId);
    }

    /**
     * Check rate limiting
     */
    checkRateLimit() {
        const now = Date.now();

        if (now > this.rateLimiter.resetTime) {
            this.rateLimiter.requests = 0;
            this.rateLimiter.resetTime = now + 60000;
        }

        if (this.rateLimiter.requests >= this.rateLimiter.maxRequests) {
            throw new Error('Rate limit exceeded. Please wait before making more requests.');
        }

        this.rateLimiter.requests++;
    }

    /**
     * Validate App credentials
     */
    validateCredentials() {
        if (!this.appId || !this.appSecret) {
            return false;
        }

        // Basic format validation
        if (this.appId.length < 10 || this.appSecret.length < 20) {
            return false;
        }

        return true;
    }

    /**
     * Lấy Tenant Access Token từ Lark API với enhanced error handling
     * @param {boolean} forceRefresh - Force refresh token
     * @returns {Promise<string>} Tenant Access Token
     */
    async getTenantAccessToken(forceRefresh = false) {
        try {
            // Check rate limiting
            this.checkRateLimit();

            // Kiểm tra cache trước
            if (!forceRefresh && this.cachedToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
                console.log('🔄 Using cached Lark token');
                return this.cachedToken;
            }

            console.log('🔑 Fetching new Lark Tenant Access Token...');

            const response = await axios.post(
                `${this.baseUrl}${this.endpoints.tenantToken}`,
                {
                    app_id: this.appId,
                    app_secret: this.appSecret
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000 // 10 seconds timeout
                }
            );

            if (response.data.code !== 0) {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

            const { tenant_access_token, expire } = response.data;

            if (!tenant_access_token) {
                throw new Error('No tenant access token received from Lark API');
            }

            // Cache token với thời gian hết hạn (trừ đi 5 phút để an toàn)
            this.cachedToken = tenant_access_token;
            this.tokenExpiry = Date.now() + (expire - 300) * 1000; // expire - 5 minutes

            console.log(`✅ Lark token obtained, expires in ${expire} seconds`);
            return tenant_access_token;

        } catch (error) {
            console.error('❌ Error getting Lark tenant access token:', error.message);

            // Provide helpful error messages
            if (error.code === 'ENOTFOUND') {
                throw new Error('Network error: Cannot reach Lark API. Check internet connection.');
            } else if (error.response?.status === 401) {
                throw new Error('Authentication failed: Invalid App ID or App Secret.');
            } else if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded: Too many requests to Lark API.');
            }

            throw new Error(`Failed to get Lark tenant access token: ${error.message}`);
        }
    }

    /**
     * Tạo authenticated headers cho Lark API calls
     * @param {object} additionalHeaders - Additional headers
     * @returns {Promise<object>} Headers với Authorization
     */
    async getAuthHeaders(additionalHeaders = {}) {
        const token = await this.getTenantAccessToken();
        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'User-Agent': 'Drive-to-Lark-Migrator/1.0',
            ...additionalHeaders
        };
    }

    /**
     * Tạo authenticated Axios instance cho Lark API với caching
     * @param {string} cacheKey - Cache key for client
     * @returns {Promise<object>} Axios instance với auth headers
     */
    async getAuthenticatedClient(cacheKey = 'default') {
        // Check cache
        if (this.clientCache.has(cacheKey)) {
            const cached = this.clientCache.get(cacheKey);
            if (cached.expires > Date.now()) {
                return cached.client;
            }
        }

        const headers = await this.getAuthHeaders();

        const client = axios.create({
            baseURL: this.baseUrl,
            headers,
            timeout: 30000, // 30 seconds timeout
            validateStatus: (status) => status < 500 // Don't throw on 4xx errors
        });

        // Add request interceptor for rate limiting
        client.interceptors.request.use((config) => {
            this.checkRateLimit();
            return config;
        });

        // Add response interceptor for error handling
        client.interceptors.response.use(
            (response) => response,
            async (error) => {
                if (error.response?.status === 401) {
                    // Token might be expired, try refresh
                    console.log('🔄 Token expired, refreshing...');
                    this.invalidateToken();

                    // Retry with new token
                    const newHeaders = await this.getAuthHeaders();
                    error.config.headers = { ...error.config.headers, ...newHeaders };
                    return axios.request(error.config);
                }
                return Promise.reject(error);
            }
        );

        // Cache client
        this.clientCache.set(cacheKey, {
            client,
            expires: Date.now() + (50 * 60 * 1000) // 50 minutes
        });

        return client;
    }

    /**
     * Comprehensive connection and permissions testing
     * @returns {Promise<object>} Detailed test results
     */
    async testConnection() {
        const result = {
            success: false,
            appInfo: null,
            permissions: [],
            errors: [],
            performance: {}
        };

        try {
            console.log('🔍 Testing Lark API connection...');

            const startTime = Date.now();
            const client = await this.getAuthenticatedClient('test');
            const authTime = Date.now() - startTime;

            // Test 1: App info
            console.log('1. Testing app info API...');
            const appResponse = await client.get(this.endpoints.appInfo);

            if (appResponse.data.code === 0) {
                result.appInfo = appResponse.data.data;
                result.permissions.push('application.info');
                console.log(`✅ App: ${result.appInfo.app_name}`);
                console.log(`📱 App Type: ${result.appInfo.app_type}`);
                console.log(`🔧 Status: ${result.appInfo.status}`);
            } else {
                result.errors.push(`App info API: ${appResponse.data.msg}`);
            }

            // Test 2: User info (if available)
            try {
                console.log('2. Testing user info API...');
                const userResponse = await client.get(this.endpoints.userInfo);

                if (userResponse.data.code === 0) {
                    result.permissions.push('contact.user.readonly');
                    console.log('✅ User info API accessible');
                } else {
                    result.errors.push(`User info API: ${userResponse.data.msg}`);
                }
            } catch (userError) {
                result.errors.push(`User info API: ${userError.message}`);
                console.log('⚠️ User info API not accessible (may need additional permissions)');
            }

            // Test 3: Drive API
            try {
                console.log('3. Testing drive API...');
                const driveResponse = await client.post(this.endpoints.driveInfo, {
                    request_docs: [
                        {
                            doc_token: "test_token",
                            doc_type: "doc"
                        }
                    ]
                });

                // Even if specific doc doesn't exist, API should respond properly
                if (driveResponse.status < 500) {
                    result.permissions.push('drive.readonly');
                    console.log('✅ Drive API accessible');
                }
            } catch (driveError) {
                result.errors.push(`Drive API: ${driveError.message}`);
                console.log('⚠️ Drive API not accessible (may need additional permissions)');
            }

            result.performance.authTime = authTime;
            result.performance.totalTime = Date.now() - startTime;
            result.success = result.permissions.length > 0;

            if (result.success) {
                console.log(`✅ Lark connection successful! Available permissions: ${result.permissions.join(', ')}`);
                console.log(`⏱️ Auth time: ${authTime}ms, Total time: ${result.performance.totalTime}ms`);
            }

        } catch (error) {
            result.errors.push(`Connection test: ${error.message}`);
            console.error('❌ Lark connection test failed:', error.message);
        }

        return result;
    }

    /**
     * Test specific Lark Drive operations
     */
    async testDriveOperations() {
        const result = {
            success: false,
            operations: [],
            errors: []
        };

        try {
            const client = await this.getAuthenticatedClient('drive-test');

            // Test upload prepare
            console.log('🔍 Testing drive upload operations...');

            try {
                const uploadPrepareResponse = await client.post(this.endpoints.uploadPrepare, {
                    file_name: "test.txt",
                    parent_type: "explorer",
                    parent_node: "root",
                    size: 100
                });

                if (uploadPrepareResponse.data.code === 0) {
                    result.operations.push('drive.upload.prepare');
                    console.log('✅ Upload prepare API accessible');
                } else {
                    result.errors.push(`Upload prepare: ${uploadPrepareResponse.data.msg}`);
                }
            } catch (uploadError) {
                result.errors.push(`Upload prepare: ${uploadError.message}`);
            }

            result.success = result.operations.length > 0;

        } catch (error) {
            result.errors.push(`Drive operations test: ${error.message}`);
        }

        return result;
    }

    /**
     * Invalidate cached token and clients (force refresh)
     */
    invalidateToken() {
        this.cachedToken = null;
        this.tokenExpiry = null;
        this.clientCache.clear();
        console.log('🔄 Lark token and client cache invalidated');
    }

    /**
     * Clear all caches
     */
    clearCache() {
        this.invalidateToken();
        console.log('🧹 All Lark caches cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            hasToken: !!this.cachedToken,
            tokenExpiry: this.tokenExpiry,
            clientsCount: this.clientCache.size,
            clientKeys: Array.from(this.clientCache.keys()),
            rateLimiter: {
                requests: this.rateLimiter.requests,
                resetTime: new Date(this.rateLimiter.resetTime),
                remaining: this.rateLimiter.maxRequests - this.rateLimiter.requests
            }
        };
    }

    /**
     * Lấy thông tin về token hiện tại
     * @returns {object} Enhanced token info
     */
    getTokenInfo() {
        const now = Date.now();
        const info = {
            hasToken: !!this.cachedToken,
            expiresAt: this.tokenExpiry ? new Date(this.tokenExpiry) : null,
            isExpired: this.tokenExpiry ? now >= this.tokenExpiry : true,
            timeToExpiry: this.tokenExpiry ? Math.max(0, this.tokenExpiry - now) : 0
        };

        if (info.timeToExpiry > 0) {
            info.timeToExpiryFormatted = this.formatDuration(info.timeToExpiry);
        }

        return info;
    }

    /**
     * Format duration in milliseconds to human readable string
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Validate Lark App setup and permissions
     */
    async validateAppSetup() {
        const result = {
            success: false,
            appId: this.appId,
            credentialsValid: false,
            connectionWorking: false,
            permissions: [],
            recommendations: [],
            errors: []
        };

        try {
            // 1. Validate credentials format
            result.credentialsValid = this.validateCredentials();

            if (!result.credentialsValid) {
                result.errors.push('Invalid App ID or App Secret format');
                result.recommendations.push('Check LARK_APP_ID and LARK_APP_SECRET in .env file');
                return result;
            }

            // 2. Test connection
            const connectionTest = await this.testConnection();
            result.connectionWorking = connectionTest.success;
            result.permissions = connectionTest.permissions;
            result.errors.push(...connectionTest.errors);

            if (!result.connectionWorking) {
                result.recommendations.push('Verify App ID and App Secret are correct');
                result.recommendations.push('Check if app is published and enabled');
                return result;
            }

            // 3. Test drive operations
            const driveTest = await this.testDriveOperations();
            result.permissions.push(...driveTest.operations);
            result.errors.push(...driveTest.errors);

            // 4. Check required permissions for migration
            const requiredPermissions = ['application.info', 'drive.readonly'];
            const missingPermissions = requiredPermissions.filter(
                perm => !result.permissions.includes(perm)
            );

            if (missingPermissions.length > 0) {
                result.recommendations.push(`Add missing permissions: ${missingPermissions.join(', ')}`);
                result.recommendations.push('Go to Lark Developer Console > App Settings > Permissions');
            }

            result.success = result.connectionWorking && missingPermissions.length === 0;

        } catch (error) {
            result.errors.push(`Validation failed: ${error.message}`);
        }

        return result;
    }

    /**
     * Get app permissions from Lark
     */
    async getAppPermissions() {
        try {
            const client = await this.getAuthenticatedClient();
            const response = await client.get('/application/v6/applications/self');

            if (response.data.code === 0) {
                return {
                    success: true,
                    permissions: response.data.data.scopes || [],
                    appInfo: response.data.data
                };
            } else {
                return {
                    success: false,
                    error: response.data.msg
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Export singleton instance
export const larkAuth = new LarkAuth();