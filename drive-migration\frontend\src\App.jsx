import React, { useState } from 'react';
import ScopeSelector from './components/ScopeSelector';
import FileList from './components/FileList';
import ScanProgress from './components/ScanProgress';
import ErrorDisplay from './components/ErrorDisplay';
import ErrorTestPanel from './components/ErrorTestPanel';
import { ToastContainer, useToast } from './components/Toast';
import { apiPost, apiGet, formatError } from './utils/apiUtils';
import './App.css';

function App() {
    const [currentStep, setCurrentStep] = useState('scope'); // scope, scanning, files, migration
    const [scanSession, setScanSession] = useState(null);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [userEmail, setUserEmail] = useState('<EMAIL>');
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(false);

    // Toast notifications
    const { toasts, removeToast, showError, showSuccess, showWarning } = useToast();

    const handleScopeSelected = async (scope, options) => {
        console.log('Scope selected:', scope, options);
        setError(null);
        setLoading(true);
        setCurrentStep('scanning');

        try {
            // Start scanning
            const result = await apiPost('/api/scan/start', {
                userEmail,
                scope,
                ...options
            });

            setScanSession(result);
            showSuccess('Bắt đầu quét thành công!');

            // Poll for scan completion
            pollScanProgress(result.sessionId);

        } catch (error) {
            console.error('Error starting scan:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi bắt đầu quét: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
            setCurrentStep('scope');
        } finally {
            setLoading(false);
        }
    };

    const pollScanProgress = async (sessionId) => {
        const interval = setInterval(async () => {
            try {
                const status = await apiGet(`/api/scan/status/${sessionId}`);

                setScanSession(status);

                if (status.status === 'completed') {
                    clearInterval(interval);
                    showSuccess(`Quét hoàn thành! Tìm thấy ${status.total_files || 0} file.`);
                    setCurrentStep('files');
                } else if (status.status === 'failed') {
                    clearInterval(interval);
                    const errorMsg = status.error_message || 'Quét thất bại';
                    setError(new Error(errorMsg));
                    showError(`Quét thất bại: ${errorMsg}`, { duration: 8000 });
                    setCurrentStep('scope');
                }
            } catch (error) {
                console.error('Error polling scan progress:', error);
                clearInterval(interval);
                const errorInfo = formatError(error);
                setError(error);
                showError(`Lỗi kiểm tra tiến trình quét: ${errorInfo.message}`, {
                    showDetails: true,
                    details: errorInfo.details,
                    duration: 8000
                });
                setCurrentStep('scope');
            }
        }, 2000);
    };

    const handleFilesSelected = (files) => {
        setSelectedFiles(files);
        setCurrentStep('migration');
    };

    const handleStartMigration = async () => {
        setError(null);
        setLoading(true);

        try {
            const result = await apiPost('/api/migration/start', {
                userEmail,
                scanSessionId: scanSession.id,
                selectedFiles: selectedFiles.map(f => f.id)
            });

            console.log('Migration started:', result);
            showSuccess(`Bắt đầu migration ${selectedFiles.length} file thành công!`);

        } catch (error) {
            console.error('Error starting migration:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi bắt đầu migration: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details,
                duration: 8000
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="app">
            <header className="app-header">
                <h1>🚀 Drive-to-Lark Migrator</h1>
                <div className="user-info">
                    <input
                        type="email"
                        placeholder="Enter your email"
                        value={userEmail}
                        onChange={(e) => setUserEmail(e.target.value)}
                        className="user-email-input"
                    />
                </div>
            </header>

            <main className="app-main">
                <div className="step-indicator">
                    <div className={`step ${currentStep === 'scope' ? 'active' : currentStep !== 'scope' ? 'completed' : ''}`}>
                        1. Select Scope
                    </div>
                    <div className={`step ${currentStep === 'scanning' ? 'active' : ''}`}>
                        2. Scanning
                    </div>
                    <div className={`step ${currentStep === 'files' ? 'active' : ''}`}>
                        3. Select Files
                    </div>
                    <div className={`step ${currentStep === 'migration' ? 'active' : ''}`}>
                        4. Migration
                    </div>
                </div>

                {/* Development Error Testing */}
                {import.meta.env.DEV && (
                    <ErrorTestPanel
                        onError={(error, testName) => {
                            const errorInfo = formatError(error);
                            setError(error);
                            showError(`${testName}: ${errorInfo.message}`, {
                                showDetails: true,
                                details: errorInfo.details,
                                duration: 10000
                            });
                        }}
                        onSuccess={(message) => {
                            showSuccess(message);
                        }}
                    />
                )}

                {error && (
                    <ErrorDisplay
                        error={error}
                        title="Lỗi trong quá trình xử lý"
                        onDismiss={() => setError(null)}
                        onRetry={() => {
                            setError(null);
                            // Retry based on current step
                            if (currentStep === 'scope') {
                                // User can retry by clicking scan again
                            }
                        }}
                    />
                )}

                <div className="step-content">
                    {currentStep === 'scope' && (
                        <ScopeSelector
                            onScopeSelected={handleScopeSelected}
                            userEmail={userEmail}
                            loading={loading}
                        />
                    )}

                    {currentStep === 'scanning' && (
                        <ScanProgress
                            scanSession={scanSession}
                            onCancel={() => setCurrentStep('scope')}
                        />
                    )}

                    {currentStep === 'files' && (
                        <FileList
                            scanSession={scanSession}
                            onFilesSelected={handleFilesSelected}
                            selectedFiles={selectedFiles}
                        />
                    )}

                    {currentStep === 'migration' && (
                        <div className="migration-step">
                            <h2>Ready to Migrate</h2>
                            <p>Selected {selectedFiles.length} files for migration</p>
                            <button
                                onClick={handleStartMigration}
                                className="btn btn-primary"
                                disabled={selectedFiles.length === 0}
                            >
                                Start Migration
                            </button>
                        </div>
                    )}
                </div>
            </main>

            {/* Toast Notifications */}
            <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
        </div>
    );
}

export default App;
