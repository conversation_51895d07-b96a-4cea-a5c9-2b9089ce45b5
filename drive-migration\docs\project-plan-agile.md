# Kế hoạch Dự án Drive-to-Lark Migrator - <PERSON><PERSON> chia Agile/Sprint

## Tổng quan Dự án
- **Tên dự án**: Drive-to-Lark Migrator
- **Thời gian dự kiến**: 13 tuần (6 sprints + setup)
- **Ph<PERSON>ơng pháp**: Agile/Scrum
- **Độ dài Sprint**: 2-3 tuần

## Bảng Tổng hợp Sprint

| Sprint | Tên Sprint                   | Thời gian | Trạng thái     | Mô tả                                               | Documentation                                                        |
| ------ | ---------------------------- | --------- | -------------- | --------------------------------------------------- | -------------------------------------------------------------------- |
| 0      | Infrastructure Setup         | 1 tuần    | 🟡 Hoàn thành*  | Thiết lập môi trường phát triển, Supabase, CI/CD    | [Sprint 0 Results](./results/sprint-0-infrastructure-setup.md)       |
| 1      | Authentication & API         | 2 tuần    | ✅ Hoàn thành   | Xây dựng hệ thống đăng nhập và tích hợp API         | [Sprint 1 Results](./results/sprint-1-authentication-api.md)         |
| 2      | Drive Scanning               | 2 tuần    | ✅ Hoàn thành   | Phát triển tính năng quét Drive và lựa chọn phạm vi | [Sprint 2 Results](./results/sprint-2-drive-scanning.md)             |
| 3      | File Migration & Permissions | 3 tuần    | ✅ Hoàn thành   | Core migration engine và hệ thống mapping quyền     | [Sprint 3 Results](./results/sprint-3-file-migration-permissions.md) |
| 4      | Real-time Progress & UI      | 2 tuần    | ✅ Hoàn thành   | Giao diện theo dõi tiến trình real-time             | [Sprint 4 Results](./results/sprint-4-realtime-progress-ui.md)       |
| 5      | Reporting & Hardening        | 2 tuần    | 🔴 Chưa bắt đầu | Hệ thống báo cáo và tăng cường bảo mật              |
| 6      | UAT & Go-live                | 1 tuần    | 🔴 Chưa bắt đầu | Kiểm thử người dùng cuối và triển khai production   |

## Chi tiết từng Sprint

### 🏗️ Sprint 0: Infrastructure Setup (1 tuần) 🟡 HOÀN THÀNH*
**Mục tiêu**: Thiết lập nền tảng kỹ thuật cho dự án

| Task                            | Estimate | Trạng thái | Mô tả                                            | Documentation                                                  |
| ------------------------------- | -------- | ---------- | ------------------------------------------------ | -------------------------------------------------------------- |
| Thiết lập Supabase Project      | 1 ngày   | ✅          | Tạo project, cấu hình database, auth, storage    | [Database Schema](./results/database-schema-implementation.md) |
| Thiết lập Repository & CI/CD    | 1 ngày   | ✅          | Git repo, GitHub Actions pipeline                | [Sprint 0 Results](./results/sprint-0-infrastructure-setup.md) |
| Thiết lập môi trường phát triển | 2 ngày   | ✅          | React + Vite frontend, Node.js backend, Docker   | [Sprint 0 Results](./results/sprint-0-infrastructure-setup.md) |
| Tạo database schema             | 1 ngày   | ✅          | Tạo bảng users, migration_tasks, migration_items | [Database Schema](./results/database-schema-implementation.md) |

**Note**: *Infrastructure 67% ready. Issues: Google Domain-wide delegation và Lark Drive permissions cần fix. [Chi tiết](./results/infrastructure-issues-fix.md)*

### 🔐 Sprint 1: Authentication & API Integration (2 tuần) ✅ HOÀN THÀNH
**Mục tiêu**: Kết nối với Google Drive và Lark APIs

| Task                        | Estimate | Trạng thái | Mô tả                                            | Documentation                                                    |
| --------------------------- | -------- | ---------- | ------------------------------------------------ | ---------------------------------------------------------------- |
| Google Service Account Auth | 3 ngày   | ✅          | Service Account JSON key, Domain-wide Delegation | [Google Auth](./results/google-auth-implementation.md)           |
| Lark Tenant Access Token    | 2 ngày   | ✅          | Hệ thống lấy và cache token                      | [Lark Auth](./results/lark-auth-implementation.md)               |
| Test Google Drive API       | 2 ngày   | ✅          | files.list, files.get, permissions.list          | [Google Drive API](./results/google-drive-api-implementation.md) |
| Test Lark Drive API         | 2 ngày   | ✅          | Upload file, tạo folder, gán quyền               | [Lark Drive API](./results/lark-drive-api-implementation.md)     |
| UI đăng nhập                | 1 ngày   | ✅          | Giao diện upload credentials                     | [UI Login](./results/ui-login-implementation.md)                 |

### 🔍 Sprint 2: Drive Scanning & Scope Selection (2 tuần) ✅ HOÀN THÀNH
**Mục tiêu**: Quét và lựa chọn dữ liệu cần di chuyển

| Task                      | Estimate | Trạng thái | Mô tả                                  | Documentation                                                                           |
| ------------------------- | -------- | ---------- | -------------------------------------- | --------------------------------------------------------------------------------------- |
| Quét toàn bộ Drive        | 3 ngày   | ✅          | files.list với supportsAllDrives=true  | [DriveScanner Service](./results/sprint-2-drive-scanning.md#drivescanner-service)       |
| Path resolver cho thư mục | 2 ngày   | ✅          | Resolve đường dẫn thành folder ID      | [PathResolver Service](./results/sprint-2-drive-scanning.md#pathresolver-service)       |
| UI lựa chọn phạm vi       | 2 ngày   | ✅          | Chọn 'Toàn bộ' hoặc 'Thư mục cụ thể'   | [ScopeSelector Component](./results/sprint-2-drive-scanning.md#scopeselector-component) |
| Hiển thị danh sách file   | 2 ngày   | ✅          | Table hiển thị file info trước migrate | [FileList Component](./results/sprint-2-drive-scanning.md#filelist-component)           |
| Xử lý giới hạn độ sâu     | 1 ngày   | ✅          | Giới hạn 100 level thư mục             | [Depth Limiting](./results/sprint-2-drive-scanning.md#xử-lý-giới-hạn-độ-sâu)            |

### 🚀 Sprint 3: File Migration & Permission Mapping (3 tuần) ✅ HOÀN THÀNH
**Mục tiêu**: Core engine di chuyển file và quyền

| Task                        | Estimate | Trạng thái | Mô tả                                      | Documentation                                                                                    |
| --------------------------- | -------- | ---------- | ------------------------------------------ | ------------------------------------------------------------------------------------------------ |
| File Download Engine        | 4 ngày   | ✅          | Tải file từ Drive (binary + Google Docs)   | [File Download Engine](./results/sprint-3-file-migration-permissions.md#file-download-engine)    |
| Lark Upload Engine          | 4 ngày   | ✅          | Upload multipart chunks lên Lark           | [Lark Upload Engine](./results/sprint-3-file-migration-permissions.md#lark-upload-engine)        |
| Hệ thống mapping người dùng | 3 ngày   | ✅          | Map email Google → Lark user ID            | [User Mapping Service](./results/sprint-3-file-migration-permissions.md#user-mapping-service)    |
| Gán quyền trên Lark         | 2 ngày   | ✅          | drive/v1/permission-public/patch           | [Permission Mapping](./results/sprint-3-file-migration-permissions.md#permission-mapping-system) |
| UI mapping quyền thủ công   | 2 ngày   | ✅          | Admin chỉnh sửa mapping chưa khớp          | [API Routes](./results/sprint-3-file-migration-permissions.md#api-routes-implementation)         |
| Xử lý lỗi và retry logic    | 3 ngày   | ✅          | Exponential back-off, retry, error logging | [Migration Engine](./results/sprint-3-file-migration-permissions.md#migration-engine)            |

### 📊 Sprint 4: Real-time Progress & UI (2 tuần) ✅ HOÀN THÀNH
**Mục tiêu**: Giao diện theo dõi tiến trình real-time

| Task                       | Estimate | Trạng thái | Mô tả                                      | Documentation                                                                                 |
| -------------------------- | -------- | ---------- | ------------------------------------------ | --------------------------------------------------------------------------------------------- |
| Tích hợp Supabase Realtime | 2 ngày   | ✅          | Realtime Broadcast cho cập nhật tiến trình | [Realtime Service](./results/sprint-4-realtime-progress-ui.md#supabase-realtime-integration)  |
| Progress Dashboard         | 3 ngày   | ✅          | % hoàn thành, tốc độ, ETA, số file lỗi     | [Progress Dashboard](./results/sprint-4-realtime-progress-ui.md#progress-dashboard-component) |
| Real-time file status      | 2 ngày   | ✅          | Trạng thái từng file đang xử lý            | [File Status Tracking](./results/sprint-4-realtime-progress-ui.md#real-time-file-status)      |
| Thông báo lỗi real-time    | 2 ngày   | ✅          | Push notification khi có file lỗi          | [Error Notifications](./results/sprint-4-realtime-progress-ui.md#error-notification-system)   |
| UI responsive              | 1 ngày   | ✅          | Thiết kế responsive, user-friendly         | [Responsive UI](./results/sprint-4-realtime-progress-ui.md#uiux-enhancements)                 |

### 📈 Sprint 5: Reporting & System Hardening (2 tuần)
**Mục tiêu**: Báo cáo và tăng cường bảo mật

| Task                        | Estimate | Trạng thái | Mô tả                                  |
| --------------------------- | -------- | ---------- | -------------------------------------- |
| Hệ thống báo cáo            | 3 ngày   | 🔴          | Báo cáo CSV/PDF với thông tin chi tiết |
| Tính năng tải xuống báo cáo | 1 ngày   | 🔴          | API endpoint và UI tải báo cáo         |
| Tăng cường bảo mật          | 2 ngày   | 🔴          | Mã hóa credentials, Supabase Vault     |
| Checkpoint & recovery       | 2 ngày   | 🔴          | Lưu checkpoint, khôi phục sau restart  |
| Performance optimization    | 2 ngày   | 🔴          | Tối ưu 500 file/phút, xử lý file lớn   |
| API rate limiting           | 2 ngày   | 🔴          | Tôn trọng giới hạn Drive API           |

### ✅ Sprint 6: UAT & Go-live (1 tuần)
**Mục tiêu**: Kiểm thử và triển khai production

| Task                     | Estimate | Trạng thái | Mô tả                                |
| ------------------------ | -------- | ---------- | ------------------------------------ |
| Chuẩn bị môi trường UAT  | 1 ngày   | 🔴          | Staging environment với dữ liệu thật |
| Kiểm thử người dùng cuối | 2 ngày   | 🔴          | Test với IT Admin, thu thập feedback |
| Performance testing      | 1 ngày   | 🔴          | Test volume lớn, stability           |
| Chuẩn bị production      | 1 ngày   | 🔴          | Production environment, domain, SSL  |
| Go-live & monitoring     | 2 ngày   | 🔴          | Triển khai, giám sát, hỗ trợ user    |

## Chú thích Trạng thái
- 🔴 **Chưa bắt đầu**: Task chưa được khởi động
- 🟡 **Đang thực hiện**: Task đang được phát triển
- 🟢 **Hoàn thành**: Task đã hoàn thành và test
- ⚫ **Bị hủy**: Task không còn cần thiết

## Rủi ro và Giải pháp
| Rủi ro                      | Mức độ     | Giải pháp                             |
| --------------------------- | ---------- | ------------------------------------- |
| Quota Drive/Lark API        | Cao        | Throttle, exponential back-off, queue |
| File > 15 GB                | Trung bình | Đánh dấu lỗi, xử lý thủ công          |
| Google Docs đặc biệt        | Thấp       | Bỏ qua hoặc xuất HTML snapshot        |
| Performance với volume lớn  | Cao        | Parallel processing, chunking         |
| Mapping người dùng phức tạp | Trung bình | UI mapping thủ công, validation       |

## Metrics và KPIs
- **Tốc độ di chuyển**: 500 file/phút (mục tiêu)
- **Uptime**: 99.9% trong quá trình migration
- **Accuracy**: 99.5% file được di chuyển thành công
- **Permission accuracy**: 95% quyền được map chính xác

## Lộ trình Timeline
```
Tuần 1: Sprint 0 - Infrastructure Setup
Tuần 2-3: Sprint 1 - Authentication & API Integration
Tuần 4-5: Sprint 2 - Drive Scanning & Scope Selection
Tuần 6-8: Sprint 3 - File Migration & Permission Mapping
Tuần 9-10: Sprint 4 - Real-time Progress & UI
Tuần 11-12: Sprint 5 - Reporting & System Hardening
Tuần 13: Sprint 6 - UAT & Go-live
```

---
*Tài liệu được tạo ngày 13-07-2025*