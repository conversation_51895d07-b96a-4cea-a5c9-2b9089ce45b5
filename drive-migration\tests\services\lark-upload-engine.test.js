/**
 * Unit Tests cho Lark Upload Engine
 * Test tất cả functionality của lark-upload-engine.js
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import fs from 'fs';
import path from 'path';
import { LarkUploadEngine } from '../../src/services/lark-upload-engine.js';
import { TestUtils, TestAssertions, mockData } from '../test-config.js';
import { larkDriveMock } from '../mocks/lark-drive-mock.js';

describe('LarkUploadEngine Service Tests', () => {
    let uploadEngine;
    let tempDir;
    
    beforeEach(() => {
        // Reset mocks
        larkDriveMock.reset();
        
        // Create upload engine instance với mocked dependencies
        uploadEngine = new LarkUploadEngine();
        uploadEngine.larkAPI = larkDriveMock;
        
        tempDir = './temp/test-uploads';
        
        // Ensure temp directory exists
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
    });
    
    afterEach(() => {
        larkDriveMock.clearCallHistory();
        
        // Remove temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    describe('Constructor và Configuration', () => {
        test('should initialize với default configuration', () => {
            const engine = new LarkUploadEngine();
            
            assert.strictEqual(engine.config.maxConcurrentUploads, 3);
            assert.strictEqual(engine.config.chunkSize, 10 * 1024 * 1024);
            assert.strictEqual(engine.config.maxRetries, 3);
            assert.strictEqual(engine.config.retryDelay, 2000);
            assert.strictEqual(engine.config.smallFileThreshold, 20 * 1024 * 1024);
            assert(Array.isArray(engine.config.supportedMimeTypes));
            assert(engine.config.supportedMimeTypes.length > 0);
        });
        
        test('should initialize stats correctly', () => {
            const engine = new LarkUploadEngine();
            
            assert.strictEqual(engine.stats.totalUploads, 0);
            assert.strictEqual(engine.stats.successfulUploads, 0);
            assert.strictEqual(engine.stats.failedUploads, 0);
            assert.strictEqual(engine.stats.activeUploads, 0);
            assert.strictEqual(engine.stats.foldersCreated, 0);
            assert(Array.isArray(engine.stats.errors));
        });
        
        test('should initialize caches correctly', () => {
            const engine = new LarkUploadEngine();
            
            assert(engine.folderCache instanceof Map);
            assert(engine.activeUploads instanceof Map);
            assert.strictEqual(engine.folderCache.size, 0);
            assert.strictEqual(engine.activeUploads.size, 0);
        });
    });

    describe('uploadFile Method', () => {
        test('should upload small file successfully', async () => {
            // Create test file
            const testFilePath = path.join(tempDir, 'small-test.txt');
            const testContent = 'Hello, World!';
            fs.writeFileSync(testFilePath, testContent);
            
            const targetFileName = 'uploaded-test.txt';
            const parentFolderId = 'test-folder-id';
            
            const result = await uploadEngine.uploadFile(testFilePath, targetFileName, parentFolderId);
            
            assert.strictEqual(result.success, true);
            assert.strictEqual(result.fileName, targetFileName);
            assert.strictEqual(result.filePath, testFilePath);
            assert(result.fileToken);
            assert(result.uploadTime >= 0);
            TestAssertions.assertValidTimestamp(result.timestamp);
            
            // Verify API was called
            const apiHistory = larkDriveMock.getCallHistory();
            const uploadCalls = apiHistory.filter(call => call.method === 'uploadFile');
            assert(uploadCalls.length > 0);
        });
        
        test('should handle progress callback', async () => {
            const testFilePath = path.join(tempDir, 'progress-test.txt');
            const testContent = 'Test content for progress tracking';
            fs.writeFileSync(testFilePath, testContent);
            
            const progressUpdates = [];
            const progressCallback = (progress) => {
                progressUpdates.push(progress);
            };
            
            await uploadEngine.uploadFile(testFilePath, 'progress-test.txt', null, progressCallback);
            
            // Should have received progress updates
            assert(progressUpdates.length > 0);
            
            // Verify progress structure
            const lastProgress = progressUpdates[progressUpdates.length - 1];
            TestAssertions.assertObjectHasKeys(lastProgress, [
                'uploadId', 'fileName', 'filePath'
            ]);
        });
        
        test('should reject non-existent files', async () => {
            const nonExistentPath = path.join(tempDir, 'non-existent.txt');
            
            const result = await uploadEngine.uploadFile(nonExistentPath, 'test.txt');
            
            assert.strictEqual(result.success, false);
            assert(result.error.includes('File not found'));
        });
        
        test('should reject files that are too large', async () => {
            const testFilePath = path.join(tempDir, 'large-test.txt');
            fs.writeFileSync(testFilePath, 'content');
            
            // Mock file stats to return large size
            const originalStatSync = fs.statSync;
            fs.statSync = (filePath) => {
                if (filePath === testFilePath) {
                    return {
                        size: 16 * 1024 * 1024 * 1024, // 16GB
                        isFile: () => true
                    };
                }
                return originalStatSync(filePath);
            };
            
            try {
                const result = await uploadEngine.uploadFile(testFilePath, 'large-test.txt');
                
                assert.strictEqual(result.success, false);
                assert(result.error.includes('File too large'));
            } finally {
                fs.statSync = originalStatSync;
            }
        });
        
        test('should update statistics correctly', async () => {
            const testFilePath = path.join(tempDir, 'stats-test.txt');
            fs.writeFileSync(testFilePath, 'test content');
            
            const initialStats = { ...uploadEngine.stats };
            
            await uploadEngine.uploadFile(testFilePath, 'stats-test.txt');
            
            assert.strictEqual(uploadEngine.stats.totalUploads, initialStats.totalUploads + 1);
            assert.strictEqual(uploadEngine.stats.successfulUploads, initialStats.successfulUploads + 1);
        });
    });

    describe('uploadMultipleFiles Method', () => {
        test('should upload multiple files successfully', async () => {
            // Create test files
            const fileList = [];
            for (let i = 0; i < 3; i++) {
                const filePath = path.join(tempDir, `test-${i}.txt`);
                fs.writeFileSync(filePath, `Content for file ${i}`);
                
                fileList.push({
                    filePath,
                    targetFileName: `uploaded-${i}.txt`,
                    parentFolderId: 'test-folder'
                });
            }
            
            const results = await uploadEngine.uploadMultipleFiles(fileList);
            
            assert(Array.isArray(results));
            assert.strictEqual(results.length, 3);
            
            // All uploads should be successful
            results.forEach(result => {
                assert.strictEqual(result.success, true);
            });
        });
        
        test('should handle batch progress callback', async () => {
            const fileList = [];
            for (let i = 0; i < 2; i++) {
                const filePath = path.join(tempDir, `batch-${i}.txt`);
                fs.writeFileSync(filePath, `Batch content ${i}`);
                
                fileList.push({
                    filePath,
                    targetFileName: `batch-${i}.txt`,
                    parentFolderId: null
                });
            }
            
            const progressUpdates = [];
            const progressCallback = (progress) => {
                progressUpdates.push(progress);
            };
            
            await uploadEngine.uploadMultipleFiles(fileList, progressCallback);
            
            // Should have received batch progress updates
            const batchUpdates = progressUpdates.filter(p => p.type === 'batch_progress');
            assert(batchUpdates.length > 0);
            
            // Verify final batch progress
            const finalBatch = batchUpdates[batchUpdates.length - 1];
            assert.strictEqual(finalBatch.completedFiles, 2);
            assert.strictEqual(finalBatch.totalFiles, 2);
            assert.strictEqual(finalBatch.progress, 100);
        });
        
        test('should handle mixed success and failure', async () => {
            const fileList = [
                {
                    filePath: path.join(tempDir, 'success.txt'),
                    targetFileName: 'success.txt',
                    parentFolderId: null
                },
                {
                    filePath: path.join(tempDir, 'non-existent.txt'),
                    targetFileName: 'failure.txt',
                    parentFolderId: null
                }
            ];
            
            // Create only the first file
            fs.writeFileSync(fileList[0].filePath, 'success content');
            
            const results = await uploadEngine.uploadMultipleFiles(fileList);
            
            assert.strictEqual(results.length, 2);
            assert.strictEqual(results[0].success, true);
            assert.strictEqual(results[1].success, false);
        });
    });

    describe('createFolderStructure Method', () => {
        test('should create simple folder', async () => {
            const folderPath = 'TestFolder';
            const parentFolderId = 'parent-folder-id';
            
            const result = await uploadEngine.createFolderStructure(folderPath, parentFolderId);
            
            assert.strictEqual(result.path, folderPath);
            assert(result.token);
            assert.strictEqual(result.parentId, parentFolderId);
            TestAssertions.assertValidTimestamp(result.createdAt);
            
            // Verify API was called
            const apiHistory = larkDriveMock.getCallHistory();
            const createCalls = apiHistory.filter(call => call.method === 'createFolder');
            assert.strictEqual(createCalls.length, 1);
        });
        
        test('should create nested folder structure', async () => {
            const folderPath = 'Documents/Projects/2025';
            
            const result = await uploadEngine.createFolderStructure(folderPath);
            
            assert.strictEqual(result.path, folderPath);
            assert(result.token);
            
            // Verify multiple folders were created
            const apiHistory = larkDriveMock.getCallHistory();
            const createCalls = apiHistory.filter(call => call.method === 'createFolder');
            assert.strictEqual(createCalls.length, 3); // Documents, Projects, 2025
            
            // Verify folder names
            const folderNames = createCalls.map(call => call.name);
            assert(folderNames.includes('Documents'));
            assert(folderNames.includes('Projects'));
            assert(folderNames.includes('2025'));
        });
        
        test('should use cache for repeated requests', async () => {
            const folderPath = 'CachedFolder';
            
            // First call
            const result1 = await uploadEngine.createFolderStructure(folderPath);
            
            // Clear API history
            larkDriveMock.clearCallHistory();
            
            // Second call should use cache
            const result2 = await uploadEngine.createFolderStructure(folderPath);
            
            assert.strictEqual(result1.token, result2.token);
            
            // Verify no additional API calls were made
            const apiHistory = larkDriveMock.getCallHistory();
            assert.strictEqual(apiHistory.length, 0);
        });
        
        test('should handle empty folder path', async () => {
            const result = await uploadEngine.createFolderStructure('');
            
            // Should return parent folder info
            assert(result);
        });
        
        test('should update folder creation stats', async () => {
            const initialCount = uploadEngine.stats.foldersCreated;
            
            await uploadEngine.createFolderStructure('Stats/Test/Folder');
            
            assert.strictEqual(uploadEngine.stats.foldersCreated, initialCount + 3);
        });
    });

    describe('Utility Methods', () => {
        test('should get correct MIME type', () => {
            const testCases = [
                { fileName: 'document.pdf', expected: 'application/pdf' },
                { fileName: 'image.jpg', expected: 'image/jpeg' },
                { fileName: 'image.jpeg', expected: 'image/jpeg' },
                { fileName: 'image.png', expected: 'image/png' },
                { fileName: 'document.docx', expected: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
                { fileName: 'spreadsheet.xlsx', expected: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
                { fileName: 'unknown.xyz', expected: 'application/octet-stream' }
            ];
            
            testCases.forEach(({ fileName, expected }) => {
                const mimeType = uploadEngine.getMimeType(fileName);
                assert.strictEqual(mimeType, expected, `Failed for ${fileName}`);
            });
        });
        
        test('should validate supported MIME types', () => {
            const supportedTypes = [
                'application/pdf',
                'image/jpeg',
                'image/png',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ];
            
            const unsupportedTypes = [
                'application/x-executable',
                'video/mp4',
                'audio/mp3'
            ];
            
            supportedTypes.forEach(mimeType => {
                assert.strictEqual(uploadEngine.isSupportedMimeType(mimeType), true);
            });
            
            unsupportedTypes.forEach(mimeType => {
                assert.strictEqual(uploadEngine.isSupportedMimeType(mimeType), false);
            });
        });
        
        test('should format file size correctly', () => {
            const testCases = [
                { bytes: 1024, expected: '1.00 KB' },
                { bytes: 1024 * 1024, expected: '1.00 MB' },
                { bytes: 1024 * 1024 * 1024, expected: '1.00 GB' },
                { bytes: 500, expected: '500 B' }
            ];
            
            testCases.forEach(({ bytes, expected }) => {
                const formatted = uploadEngine.formatFileSize(bytes);
                assert.strictEqual(formatted, expected);
            });
        });
        
        test('should return stats correctly', () => {
            const stats = uploadEngine.getStats();
            
            TestAssertions.assertObjectHasKeys(stats, [
                'totalUploads',
                'successfulUploads',
                'failedUploads',
                'activeUploads',
                'foldersCreated',
                'errors'
            ]);
            
            assert(typeof stats.totalUploads === 'number');
            assert(typeof stats.successfulUploads === 'number');
            assert(typeof stats.failedUploads === 'number');
            assert(typeof stats.activeUploads === 'number');
            assert(typeof stats.foldersCreated === 'number');
            assert(Array.isArray(stats.errors));
        });
        
        test('should reset stats correctly', () => {
            // Add some stats
            uploadEngine.stats.totalUploads = 5;
            uploadEngine.stats.successfulUploads = 3;
            uploadEngine.stats.failedUploads = 2;
            uploadEngine.stats.foldersCreated = 10;
            uploadEngine.stats.errors.push({ error: 'test error' });
            
            uploadEngine.resetStats();
            
            assert.strictEqual(uploadEngine.stats.totalUploads, 0);
            assert.strictEqual(uploadEngine.stats.successfulUploads, 0);
            assert.strictEqual(uploadEngine.stats.failedUploads, 0);
            assert.strictEqual(uploadEngine.stats.foldersCreated, 0);
            assert.strictEqual(uploadEngine.stats.errors.length, 0);
        });
    });

    describe('Error Handling', () => {
        test('should handle Lark API errors', async () => {
            const testFilePath = path.join(tempDir, 'error-test.txt');
            fs.writeFileSync(testFilePath, 'test content');
            
            // Simulate API error
            const restoreError = larkDriveMock.simulateError(
                'uploadFile',
                new Error('Lark API quota exceeded')
            );
            
            try {
                const result = await uploadEngine.uploadFile(testFilePath, 'error-test.txt');
                
                assert.strictEqual(result.success, false);
                assert(result.error.includes('Lark API quota exceeded'));
                assert.strictEqual(uploadEngine.stats.failedUploads, 1);
            } finally {
                restoreError();
            }
        });
        
        test('should handle network timeouts', async () => {
            const testFilePath = path.join(tempDir, 'timeout-test.txt');
            fs.writeFileSync(testFilePath, 'test content');
            
            // Simulate network error
            const restoreError = larkDriveMock.simulateNetworkError('uploadFile', 1.0);
            
            try {
                const result = await uploadEngine.uploadFile(testFilePath, 'timeout-test.txt');
                
                assert.strictEqual(result.success, false);
                assert(result.error.includes('Network error'));
            } finally {
                restoreError();
            }
        });
        
        test('should track errors in stats', async () => {
            const testFilePath = path.join(tempDir, 'stats-error-test.txt');
            fs.writeFileSync(testFilePath, 'test content');
            
            const restoreError = larkDriveMock.simulateError(
                'uploadFile',
                new Error('Test error')
            );
            
            try {
                await uploadEngine.uploadFile(testFilePath, 'stats-error-test.txt');
                
                assert.strictEqual(uploadEngine.stats.errors.length, 1);
                
                const error = uploadEngine.stats.errors[0];
                assert.strictEqual(error.fileName, 'stats-error-test.txt');
                assert.strictEqual(error.filePath, testFilePath);
                assert.strictEqual(error.error, 'Test error');
                TestAssertions.assertValidTimestamp(error.timestamp);
            } finally {
                restoreError();
            }
        });
        
        test('should handle folder creation errors', async () => {
            // Simulate folder creation error
            const restoreError = larkDriveMock.simulateError(
                'createFolder',
                new Error('Folder creation failed')
            );
            
            try {
                await assert.rejects(
                    uploadEngine.createFolderStructure('ErrorFolder'),
                    /Failed to create folder structure/
                );
            } finally {
                restoreError();
            }
        });
    });

    describe('Cache Management', () => {
        test('should cache folder creation results', () => {
            const folderPath = 'TestCache';
            const folderInfo = {
                path: folderPath,
                token: 'test-token-123',
                parentId: null,
                createdAt: new Date().toISOString()
            };
            
            const cacheKey = `root:${folderPath}`;
            uploadEngine.folderCache.set(cacheKey, folderInfo);
            
            assert(uploadEngine.folderCache.has(cacheKey));
            assert.strictEqual(uploadEngine.folderCache.get(cacheKey).token, 'test-token-123');
        });
        
        test('should clear folder cache', () => {
            // Add some cache entries
            uploadEngine.folderCache.set('key1', { token: 'token1' });
            uploadEngine.folderCache.set('key2', { token: 'token2' });
            
            assert.strictEqual(uploadEngine.folderCache.size, 2);
            
            uploadEngine.clearFolderCache();
            
            assert.strictEqual(uploadEngine.folderCache.size, 0);
        });
    });

    describe('Concurrent Uploads', () => {
        test('should track active uploads', async () => {
            const testFilePath = path.join(tempDir, 'concurrent-test.txt');
            fs.writeFileSync(testFilePath, 'test content');
            
            assert.strictEqual(uploadEngine.stats.activeUploads, 0);
            
            const uploadPromise = uploadEngine.uploadFile(testFilePath, 'concurrent-test.txt');
            
            // During upload, active count should be 1
            // Note: This is a simplified test, in real scenario we'd need to check during actual upload
            
            await uploadPromise;
            
            // After upload, active count should be back to 0
            assert.strictEqual(uploadEngine.stats.activeUploads, 0);
        });
        
        test('should respect concurrent upload limits', () => {
            assert.strictEqual(uploadEngine.config.maxConcurrentUploads, 3);
            
            // In a real implementation, we would test that no more than 3 uploads
            // happen simultaneously, but this requires more complex async testing
            assert(true, 'Concurrent limit configuration is correct');
        });
    });
});
