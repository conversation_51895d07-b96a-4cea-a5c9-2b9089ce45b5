import { supabaseClient } from './database/supabase.js';
import { migrationService } from './database/migration-service.js';

/**
 * Test Database Connection và Operations
 */
async function testDatabaseConnection() {
    console.log('🔍 Testing Supabase Database Connection...\n');

    try {
        // Test basic connection
        console.log('1. Testing basic connection...');
        const connectionTest = await supabaseClient.testConnection();
        
        if (connectionTest.success) {
            console.log('✅ Database connection successful');
        } else {
            console.log('❌ Database connection failed:', connectionTest.error);
            return;
        }

        // Test user mapping operations
        console.log('\n2. Testing user mapping operations...');
        
        const testEmail = '<EMAIL>';
        const testLarkUserId = 'ou_test123';

        // Create user mapping
        const createUserResult = await supabaseClient.createUserMapping(testEmail, testLarkUserId);
        if (createUserResult.success) {
            console.log('✅ User mapping created:', createUserResult.data);
        } else {
            console.log('❌ Failed to create user mapping:', createUserResult.error);
        }

        // Get user mapping
        const getUserResult = await supabaseClient.getUserMapping(testEmail);
        if (getUserResult.success) {
            console.log('✅ User mapping retrieved:', getUserResult.data);
        } else {
            console.log('❌ Failed to get user mapping:', getUserResult.error);
        }

        // Test migration service
        console.log('\n3. Testing migration service...');
        
        const taskResult = await migrationService.initializeMigrationTask(
            testEmail,
            'path',
            '/test-folder',
            { max_retries: 5 }
        );

        if (taskResult.success) {
            console.log('✅ Migration task created:', taskResult.task);
            
            const taskId = taskResult.task.id;

            // Start task
            const startResult = await migrationService.startMigrationTask(taskId);
            if (startResult.success) {
                console.log('✅ Migration task started');
            }

            // Add test file
            const fileResult = await migrationService.addFileToMigration(taskId, {
                id: 'test_file_id_123',
                name: 'test-document.pdf',
                path: '/test-folder/test-document.pdf',
                mimeType: 'application/pdf',
                size: '1024000',
                webViewLink: 'https://drive.google.com/file/d/test_file_id_123/view',
                modifiedTime: new Date().toISOString(),
                createdTime: new Date().toISOString(),
                owners: [{ emailAddress: testEmail }],
                permissions: []
            });

            if (fileResult.success) {
                console.log('✅ Test file added to migration:', fileResult.data);
                
                const itemId = fileResult.data.id;

                // Update file status
                await migrationService.updateFileStatus(itemId, 'downloading');
                console.log('✅ File status updated to downloading');

                await migrationService.updateFileStatus(itemId, 'uploading');
                console.log('✅ File status updated to uploading');

                await migrationService.updateFileStatus(itemId, 'completed', {
                    dest_file_id: 'lark_file_token_456'
                });
                console.log('✅ File status updated to completed');
            }

            // Update task progress
            const progressResult = await migrationService.updateTaskProgress(taskId, 100, {
                completed_files: 1,
                total_files: 1
            });

            if (progressResult.success) {
                console.log('✅ Task progress updated');
            }

            // Complete task
            const completeResult = await migrationService.completeMigrationTask(taskId, {
                total_files: 1,
                completed_files: 1,
                failed_files: 0
            });

            if (completeResult.success) {
                console.log('✅ Migration task completed');
            }

            // Get task statistics
            const statsResult = await migrationService.getTaskStatistics(taskId);
            if (statsResult.success) {
                console.log('✅ Task statistics:', JSON.stringify(statsResult.data.statistics, null, 2));
            }

        } else {
            console.log('❌ Failed to create migration task:', taskResult.error);
        }

        // Test realtime subscription
        console.log('\n4. Testing realtime subscription...');
        
        const subscription = supabaseClient.subscribeToTask('test-task-id', (payload) => {
            console.log('📡 Realtime update received:', payload);
        });

        console.log('✅ Realtime subscription created');
        
        // Cleanup subscription
        setTimeout(() => {
            supabaseClient.unsubscribe(subscription);
            console.log('✅ Realtime subscription cleaned up');
        }, 1000);

        console.log('\n🎉 All database tests completed successfully!');

    } catch (error) {
        console.error('❌ Database test failed:', error);
    }
}

/**
 * Test Database Schema
 */
async function testDatabaseSchema() {
    console.log('\n🔍 Testing Database Schema...\n');

    try {
        const client = supabaseClient.getServiceClient();

        // Test each table exists
        const tables = ['users', 'migration_tasks', 'migration_items', 'permission_mappings', 'migration_logs'];
        
        for (const table of tables) {
            console.log(`Testing table: ${table}`);
            
            const { data, error } = await client
                .from(table)
                .select('*')
                .limit(1);

            if (error) {
                console.log(`❌ Table ${table} error:`, error.message);
            } else {
                console.log(`✅ Table ${table} accessible`);
            }
        }

        // Test view
        console.log('\nTesting view: migration_task_stats');
        const { data: viewData, error: viewError } = await client
            .from('migration_task_stats')
            .select('*')
            .limit(1);

        if (viewError) {
            console.log('❌ View migration_task_stats error:', viewError.message);
        } else {
            console.log('✅ View migration_task_stats accessible');
        }

        console.log('\n🎉 Database schema test completed!');

    } catch (error) {
        console.error('❌ Schema test failed:', error);
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Database Tests\n');
    console.log('=' .repeat(50));
    
    await testDatabaseConnection();
    await testDatabaseSchema();
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ All tests completed!');
    
    // Exit process
    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testDatabaseConnection, testDatabaseSchema };
