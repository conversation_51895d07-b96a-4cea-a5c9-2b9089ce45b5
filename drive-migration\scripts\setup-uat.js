#!/usr/bin/env node

/**
 * UAT Environment Setup Script
 * Thi<PERSON>t lập môi trường UAT cho Drive-to-Lark Migrator
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load UAT environment
const uatEnvPath = join(__dirname, '../config/.env.uat');
if (existsSync(uatEnvPath)) {
    dotenv.config({ path: uatEnvPath });
} else {
    console.error('❌ UAT environment file not found. Please copy config/uat.env.example to config/.env.uat');
    process.exit(1);
}

class UATSetup {
    constructor() {
        this.supabase = null;
        this.setupResults = {
            database: false,
            testData: false,
            monitoring: false,
            logging: false
        };
    }

    /**
     * Initialize UAT setup
     */
    async initialize() {
        console.log('🚀 Starting UAT Environment Setup...\n');

        try {
            // 1. Validate environment variables
            await this.validateEnvironment();

            // 2. Setup database connection
            await this.setupDatabase();

            // 3. Create UAT test data
            await this.createTestData();

            // 4. Setup monitoring
            await this.setupMonitoring();

            // 5. Setup logging
            await this.setupLogging();

            // 6. Validate setup
            await this.validateSetup();

            this.printResults();

        } catch (error) {
            console.error('❌ UAT setup failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Validate required environment variables
     */
    async validateEnvironment() {
        console.log('1. 🔧 Validating Environment Variables...');

        const required = [
            'SUPABASE_URL',
            'SUPABASE_SERVICE_ROLE_KEY',
            'LARK_APP_ID',
            'LARK_APP_SECRET'
        ];

        const missing = required.filter(key => !process.env[key]);

        if (missing.length > 0) {
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }

        console.log('✅ Environment variables validated\n');
    }

    /**
     * Setup database connection and schema
     */
    async setupDatabase() {
        console.log('2. 🗄️ Setting up UAT Database...');

        try {
            this.supabase = createClient(
                process.env.SUPABASE_URL,
                process.env.SUPABASE_SERVICE_ROLE_KEY
            );

            // Test connection
            const { data, error } = await this.supabase
                .from('migration_tasks')
                .select('count')
                .limit(1);

            if (error) {
                throw new Error(`Database connection failed: ${error.message}`);
            }

            console.log('✅ Database connection established');

            // Create UAT-specific tables if needed
            await this.createUATTables();

            this.setupResults.database = true;
            console.log('✅ UAT Database setup completed\n');

        } catch (error) {
            throw new Error(`Database setup failed: ${error.message}`);
        }
    }

    /**
     * Create UAT-specific tables
     */
    async createUATTables() {
        try {
            // Create UAT test sessions table
            const { error } = await this.supabase.rpc('create_uat_tables');

            if (error && !error.message.includes('already exists')) {
                console.warn('⚠️ UAT tables creation warning:', error.message);
            }

        } catch (error) {
            console.warn('⚠️ UAT tables setup warning:', error.message);
        }
    }

    /**
     * Create test data for UAT
     */
    async createTestData() {
        console.log('3. 📊 Creating UAT Test Data...');

        try {
            const testDataSize = parseInt(process.env.UAT_TEST_DATA_SIZE) || 100;

            // Create test migration task
            const { data: task, error: taskError } = await this.supabase
                .from('migration_tasks')
                .insert({
                    user_email: process.env.GOOGLE_TEST_EMAIL,
                    status: 'pending',
                    total_files: testDataSize,
                    total_size: testDataSize * 1024 * 1024, // 1MB per file average
                    options: {
                        uat_test: true,
                        test_data_size: testDataSize
                    }
                })
                .select()
                .single();

            if (taskError) {
                throw new Error(`Test task creation failed: ${taskError.message}`);
            }

            console.log(`✅ Test migration task created: ${task.id}`);

            // Create test migration items
            const testItems = Array.from({ length: Math.min(testDataSize, 50) }, (_, i) => ({
                task_id: task.id,
                google_file_id: `uat_test_file_${i + 1}`,
                file_name: `UAT Test File ${i + 1}.txt`,
                file_size: 1024 * (i + 1),
                mime_type: 'text/plain',
                status: 'pending'
            }));

            const { error: itemsError } = await this.supabase
                .from('migration_items')
                .insert(testItems);

            if (itemsError) {
                throw new Error(`Test items creation failed: ${itemsError.message}`);
            }

            console.log(`✅ ${testItems.length} test migration items created`);
            this.setupResults.testData = true;
            console.log('✅ UAT Test data created\n');

        } catch (error) {
            throw new Error(`Test data creation failed: ${error.message}`);
        }
    }

    /**
     * Setup monitoring for UAT
     */
    async setupMonitoring() {
        console.log('4. 📊 Setting up UAT Monitoring...');

        try {
            // Create logs directory
            const logsDir = join(__dirname, '../logs');
            if (!existsSync(logsDir)) {
                mkdirSync(logsDir, { recursive: true });
            }

            // Create monitoring configuration
            const monitoringConfig = {
                enabled: process.env.UAT_MONITORING_ENABLED === 'true',
                webhookUrl: process.env.MONITORING_WEBHOOK_URL,
                alertEmail: process.env.ALERT_EMAIL,
                metrics: {
                    performance: true,
                    errors: true,
                    usage: true
                }
            };

            const configPath = join(__dirname, '../config/uat-monitoring.json');
            writeFileSync(configPath, JSON.stringify(monitoringConfig, null, 2));

            console.log('✅ Monitoring configuration created');
            this.setupResults.monitoring = true;
            console.log('✅ UAT Monitoring setup completed\n');

        } catch (error) {
            throw new Error(`Monitoring setup failed: ${error.message}`);
        }
    }

    /**
     * Setup logging for UAT
     */
    async setupLogging() {
        console.log('5. 📝 Setting up UAT Logging...');

        try {
            const logsDir = join(__dirname, '../logs');

            // Create log files
            const logFiles = ['uat.log', 'uat-errors.log', 'uat-performance.log'];

            logFiles.forEach(file => {
                const filePath = join(logsDir, file);
                if (!existsSync(filePath)) {
                    writeFileSync(filePath, `# UAT Log File - Created ${new Date().toISOString()}\n`);
                }
            });

            console.log('✅ Log files created');
            this.setupResults.logging = true;
            console.log('✅ UAT Logging setup completed\n');

        } catch (error) {
            throw new Error(`Logging setup failed: ${error.message}`);
        }
    }

    /**
     * Validate UAT setup
     */
    async validateSetup() {
        console.log('6. ✅ Validating UAT Setup...');

        try {
            // Test database queries
            const { data: tasks } = await this.supabase
                .from('migration_tasks')
                .select('*')
                .limit(1);

            const { data: items } = await this.supabase
                .from('migration_items')
                .select('*')
                .limit(1);

            console.log('✅ Database queries working');
            console.log('✅ UAT setup validation completed\n');

        } catch (error) {
            throw new Error(`Setup validation failed: ${error.message}`);
        }
    }

    /**
     * Print setup results
     */
    printResults() {
        console.log('🎉 UAT Environment Setup Results:');
        console.log('='.repeat(50));

        Object.entries(this.setupResults).forEach(([component, success]) => {
            const status = success ? '✅' : '❌';
            const name = component.charAt(0).toUpperCase() + component.slice(1);
            console.log(`${status} ${name}: ${success ? 'SUCCESS' : 'FAILED'}`);
        });

        const allSuccess = Object.values(this.setupResults).every(Boolean);

        console.log('\n' + '='.repeat(50));
        console.log(`🎯 Overall Status: ${allSuccess ? '✅ SUCCESS' : '❌ FAILED'}`);

        if (allSuccess) {
            console.log('\n🚀 UAT Environment is ready for testing!');
            console.log('\nNext steps:');
            console.log('1. Start UAT server: npm run start:uat');
            console.log('2. Run UAT tests: npm run test:uat');
            console.log('3. Access UAT dashboard: http://localhost:3001');
        }
    }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const setup = new UATSetup();
    setup.initialize().catch(console.error);
}

export default UATSetup;
