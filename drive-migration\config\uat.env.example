# UAT Environment Configuration
# Copy this file to .env.uat for UAT testing

# Environment
NODE_ENV=uat
PORT=3001
UAT_MODE=true

# UAT Database (Supabase UAT Project)
SUPABASE_URL=https://your-uat-project.supabase.co
SUPABASE_ANON_KEY=your-uat-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-uat-service-role-key

# Google Service Account (UAT)
# Google credentials are loaded from google-service-account.json file
# No need for environment variables for Google authentication

# Google Admin & Test Users (UAT) - optional, can be passed as command line arguments
# GOOGLE_ADMIN_EMAIL=<EMAIL>
# GOOGLE_TEST_EMAIL=<EMAIL>

# Lark App (UAT)
LARK_APP_ID=cli_your_uat_app_id
LARK_APP_SECRET=your_uat_app_secret

# UAT Specific Settings
UAT_TEST_DATA_SIZE=1000
UAT_MAX_FILE_SIZE=100MB
UAT_CONCURRENT_USERS=5
UAT_MONITORING_ENABLED=true
UAT_DEBUG_MODE=true

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/uat.log
ERROR_LOG_FILE=logs/uat-errors.log

# Performance Testing
PERFORMANCE_TEST_ENABLED=true
LOAD_TEST_DURATION=300
LOAD_TEST_CONCURRENT_USERS=10

# Monitoring & Alerts
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
ALERT_EMAIL=<EMAIL>

# Security
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# Backup & Recovery
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600000
BACKUP_RETENTION_DAYS=7
