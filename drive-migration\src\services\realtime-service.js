import { supabaseClient } from '../database/supabase.js';

/**
 * Realtime Service
 * Supabase Realtime integration cho real-time progress tracking và notifications
 * Sử dụng Supabase Broadcast để gửi real-time updates cho frontend
 */
export class RealtimeService {
    constructor() {
        this.supabase = supabaseClient;
        this.channels = new Map(); // Track active channels
        this.subscribers = new Map(); // Track subscribers
        
        // Configuration
        this.config = {
            channelPrefix: 'migration_',
            maxChannels: 100,
            heartbeatInterval: 30000, // 30 seconds
            reconnectDelay: 5000 // 5 seconds
        };

        // Statistics
        this.stats = {
            activeChannels: 0,
            totalMessages: 0,
            totalSubscribers: 0,
            errors: 0
        };

        console.log('🔄 Realtime Service initialized');
    }

    /**
     * Create migration progress channel
     * @param {string} migrationId - Migration ID
     * @returns {object} Channel object
     */
    createMigrationChannel(migrationId) {
        const channelName = `${this.config.channelPrefix}${migrationId}`;
        
        try {
            // Check if channel already exists
            if (this.channels.has(channelName)) {
                console.log(`📡 Reusing existing channel: ${channelName}`);
                return this.channels.get(channelName);
            }

            // Create new channel
            const channel = this.supabase.channel(channelName, {
                config: {
                    broadcast: { self: true },
                    presence: { key: migrationId }
                }
            });

            // Setup channel event handlers
            this.setupChannelHandlers(channel, channelName);

            // Subscribe to channel
            channel.subscribe((status) => {
                console.log(`📡 Channel ${channelName} status:`, status);
                
                if (status === 'SUBSCRIBED') {
                    this.stats.activeChannels++;
                    console.log(`✅ Successfully subscribed to ${channelName}`);
                } else if (status === 'CHANNEL_ERROR') {
                    this.stats.errors++;
                    console.error(`❌ Channel error for ${channelName}`);
                } else if (status === 'TIMED_OUT') {
                    console.warn(`⏰ Channel timeout for ${channelName}`);
                } else if (status === 'CLOSED') {
                    this.stats.activeChannels--;
                    console.log(`🔒 Channel closed: ${channelName}`);
                }
            });

            // Store channel
            this.channels.set(channelName, {
                channel,
                migrationId,
                createdAt: new Date().toISOString(),
                subscribers: new Set()
            });

            console.log(`📡 Created migration channel: ${channelName}`);
            return this.channels.get(channelName);

        } catch (error) {
            console.error(`❌ Error creating channel ${channelName}:`, error.message);
            this.stats.errors++;
            throw error;
        }
    }

    /**
     * Setup channel event handlers
     * @param {object} channel - Supabase channel
     * @param {string} channelName - Channel name
     */
    setupChannelHandlers(channel, channelName) {
        // Handle broadcast messages
        channel.on('broadcast', { event: '*' }, (payload) => {
            console.log(`📨 Broadcast received on ${channelName}:`, payload.event);
            this.stats.totalMessages++;
            
            // Forward to subscribers
            const channelInfo = this.channels.get(channelName);
            if (channelInfo) {
                channelInfo.subscribers.forEach(callback => {
                    try {
                        callback(payload);
                    } catch (error) {
                        console.error('❌ Error in subscriber callback:', error.message);
                    }
                });
            }
        });

        // Handle presence changes
        channel.on('presence', { event: 'sync' }, () => {
            const state = channel.presenceState();
            console.log(`👥 Presence sync on ${channelName}:`, Object.keys(state).length, 'users');
        });

        channel.on('presence', { event: 'join' }, ({ key, newPresences }) => {
            console.log(`👋 User joined ${channelName}:`, key);
        });

        channel.on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
            console.log(`👋 User left ${channelName}:`, key);
        });
    }

    /**
     * Subscribe to migration progress updates
     * @param {string} migrationId - Migration ID
     * @param {function} callback - Callback function for updates
     * @returns {function} Unsubscribe function
     */
    subscribeMigrationProgress(migrationId, callback) {
        try {
            const channelInfo = this.createMigrationChannel(migrationId);
            
            // Add subscriber
            channelInfo.subscribers.add(callback);
            this.stats.totalSubscribers++;
            
            console.log(`📡 Subscribed to migration progress: ${migrationId}`);

            // Return unsubscribe function
            return () => {
                channelInfo.subscribers.delete(callback);
                this.stats.totalSubscribers--;
                console.log(`📡 Unsubscribed from migration progress: ${migrationId}`);
                
                // Cleanup channel if no more subscribers
                if (channelInfo.subscribers.size === 0) {
                    this.cleanupChannel(migrationId);
                }
            };

        } catch (error) {
            console.error(`❌ Error subscribing to migration ${migrationId}:`, error.message);
            throw error;
        }
    }

    /**
     * Broadcast migration progress update
     * @param {string} migrationId - Migration ID
     * @param {object} progressData - Progress data
     */
    async broadcastMigrationProgress(migrationId, progressData) {
        try {
            const channelName = `${this.config.channelPrefix}${migrationId}`;
            const channelInfo = this.channels.get(channelName);

            if (!channelInfo) {
                console.warn(`⚠️ No channel found for migration: ${migrationId}`);
                return;
            }

            const payload = {
                type: 'migration_progress',
                migrationId,
                timestamp: new Date().toISOString(),
                ...progressData
            };

            await channelInfo.channel.send({
                type: 'broadcast',
                event: 'migration_progress',
                payload
            });

            console.log(`📤 Broadcasted progress for ${migrationId}:`, progressData.type || 'update');

        } catch (error) {
            console.error(`❌ Error broadcasting progress for ${migrationId}:`, error.message);
            this.stats.errors++;
        }
    }

    /**
     * Broadcast file progress update
     * @param {string} migrationId - Migration ID
     * @param {object} fileProgress - File progress data
     */
    async broadcastFileProgress(migrationId, fileProgress) {
        try {
            const payload = {
                type: 'file_progress',
                migrationId,
                fileId: fileProgress.fileId,
                fileName: fileProgress.fileName,
                phase: fileProgress.phase, // 'downloading', 'uploading', 'completed', 'failed'
                progress: fileProgress.progress,
                speed: fileProgress.speed,
                timestamp: new Date().toISOString()
            };

            await this.broadcastMigrationProgress(migrationId, payload);

        } catch (error) {
            console.error(`❌ Error broadcasting file progress:`, error.message);
        }
    }

    /**
     * Broadcast batch progress update
     * @param {string} migrationId - Migration ID
     * @param {object} batchProgress - Batch progress data
     */
    async broadcastBatchProgress(migrationId, batchProgress) {
        try {
            const payload = {
                type: 'batch_progress',
                migrationId,
                processedFiles: batchProgress.processedFiles,
                totalFiles: batchProgress.totalFiles,
                successfulFiles: batchProgress.successfulFiles,
                failedFiles: batchProgress.failedFiles,
                progress: batchProgress.progress,
                estimatedTimeRemaining: batchProgress.estimatedTimeRemaining,
                timestamp: new Date().toISOString()
            };

            await this.broadcastMigrationProgress(migrationId, payload);

        } catch (error) {
            console.error(`❌ Error broadcasting batch progress:`, error.message);
        }
    }

    /**
     * Broadcast error notification
     * @param {string} migrationId - Migration ID
     * @param {object} errorData - Error data
     */
    async broadcastError(migrationId, errorData) {
        try {
            const payload = {
                type: 'error',
                migrationId,
                severity: errorData.severity || 'error', // 'warning', 'error', 'critical'
                fileId: errorData.fileId,
                fileName: errorData.fileName,
                errorMessage: errorData.errorMessage,
                errorCode: errorData.errorCode,
                retryable: errorData.retryable || false,
                timestamp: new Date().toISOString()
            };

            await this.broadcastMigrationProgress(migrationId, payload);

        } catch (error) {
            console.error(`❌ Error broadcasting error notification:`, error.message);
        }
    }

    /**
     * Broadcast migration status change
     * @param {string} migrationId - Migration ID
     * @param {string} status - New status
     * @param {object} metadata - Additional metadata
     */
    async broadcastStatusChange(migrationId, status, metadata = {}) {
        try {
            const payload = {
                type: 'status_change',
                migrationId,
                status,
                previousStatus: metadata.previousStatus,
                reason: metadata.reason,
                timestamp: new Date().toISOString(),
                ...metadata
            };

            await this.broadcastMigrationProgress(migrationId, payload);

        } catch (error) {
            console.error(`❌ Error broadcasting status change:`, error.message);
        }
    }

    /**
     * Broadcast migration completion
     * @param {string} migrationId - Migration ID
     * @param {object} results - Migration results
     */
    async broadcastMigrationComplete(migrationId, results) {
        try {
            const payload = {
                type: 'migration_complete',
                migrationId,
                success: results.success,
                totalFiles: results.totalFiles,
                successfulFiles: results.successfulFiles,
                failedFiles: results.failedFiles,
                duration: results.duration,
                timestamp: new Date().toISOString()
            };

            await this.broadcastMigrationProgress(migrationId, payload);

            // Cleanup channel after completion
            setTimeout(() => {
                this.cleanupChannel(migrationId);
            }, 30000); // 30 seconds delay

        } catch (error) {
            console.error(`❌ Error broadcasting migration completion:`, error.message);
        }
    }

    /**
     * Get channel presence (active users)
     * @param {string} migrationId - Migration ID
     * @returns {object} Presence state
     */
    getChannelPresence(migrationId) {
        try {
            const channelName = `${this.config.channelPrefix}${migrationId}`;
            const channelInfo = this.channels.get(channelName);

            if (!channelInfo) {
                return { users: [], count: 0 };
            }

            const state = channelInfo.channel.presenceState();
            const users = Object.keys(state);

            return {
                users,
                count: users.length,
                state
            };

        } catch (error) {
            console.error(`❌ Error getting channel presence:`, error.message);
            return { users: [], count: 0 };
        }
    }

    /**
     * Cleanup migration channel
     * @param {string} migrationId - Migration ID
     */
    async cleanupChannel(migrationId) {
        try {
            const channelName = `${this.config.channelPrefix}${migrationId}`;
            const channelInfo = this.channels.get(channelName);

            if (!channelInfo) {
                return;
            }

            // Unsubscribe from channel
            await channelInfo.channel.unsubscribe();
            
            // Remove from tracking
            this.channels.delete(channelName);
            
            console.log(`🗑️ Cleaned up channel: ${channelName}`);

        } catch (error) {
            console.error(`❌ Error cleaning up channel:`, error.message);
        }
    }

    /**
     * Cleanup all channels
     */
    async cleanupAllChannels() {
        try {
            console.log(`🗑️ Cleaning up ${this.channels.size} channels...`);

            const cleanupPromises = Array.from(this.channels.keys()).map(channelName => {
                const migrationId = channelName.replace(this.config.channelPrefix, '');
                return this.cleanupChannel(migrationId);
            });

            await Promise.all(cleanupPromises);
            
            console.log('✅ All channels cleaned up');

        } catch (error) {
            console.error('❌ Error cleaning up all channels:', error.message);
        }
    }

    /**
     * Get realtime service statistics
     * @returns {object} Service statistics
     */
    getStats() {
        return {
            ...this.stats,
            activeChannels: this.channels.size,
            channelNames: Array.from(this.channels.keys())
        };
    }

    /**
     * Health check for realtime service
     * @returns {object} Health status
     */
    async healthCheck() {
        try {
            // Test channel creation and cleanup
            const testChannelName = 'test_health_check';
            const testChannel = this.supabase.channel(testChannelName);
            
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('Health check timeout')), 5000);
                
                testChannel.subscribe((status) => {
                    if (status === 'SUBSCRIBED') {
                        clearTimeout(timeout);
                        resolve();
                    } else if (status === 'CHANNEL_ERROR') {
                        clearTimeout(timeout);
                        reject(new Error('Channel subscription failed'));
                    }
                });
            });

            await testChannel.unsubscribe();

            return {
                healthy: true,
                timestamp: new Date().toISOString(),
                stats: this.getStats()
            };

        } catch (error) {
            return {
                healthy: false,
                error: error.message,
                timestamp: new Date().toISOString(),
                stats: this.getStats()
            };
        }
    }
}

// Export singleton instance
export const realtimeService = new RealtimeService();
