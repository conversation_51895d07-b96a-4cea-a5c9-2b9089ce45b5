/**
 * Lark Drive API Mock
 * Mock implementation cho Lark Drive API để testing
 */

import { TestUtils, mockData } from '../test-config.js';

export class LarkDriveMock {
    constructor() {
        this.files = new Map();
        this.folders = new Map();
        this.uploadSessions = new Map();
        this.callHistory = [];
        
        // Initialize với mock data
        this.initializeMockData();
    }
    
    initializeMockData() {
        // Tạo root folder
        const rootFolder = {
            token: 'root',
            name: 'Root',
            type: 'folder',
            parent_token: '',
            created_time: '2023-01-01T00:00:00Z',
            modified_time: '2023-01-01T00:00:00Z'
        };
        this.folders.set('root', rootFolder);
        
        // Tạo một số test folders
        for (let i = 0; i < 3; i++) {
            const folder = {
                token: TestUtils.generateUUID(),
                name: `Test Folder ${i + 1}`,
                type: 'folder',
                parent_token: 'root',
                created_time: new Date().toISOString(),
                modified_time: new Date().toISOString()
            };
            this.folders.set(folder.token, folder);
        }
    }
    
    // Mock Lark Drive API methods
    async getTenantAccessToken() {
        this.callHistory.push({ method: 'getTenantAccessToken' });
        
        return {
            code: 0,
            msg: 'success',
            tenant_access_token: 'mock-tenant-token-' + Date.now(),
            expire: 7200
        };
    }
    
    async createFolder(name, parentToken = 'root') {
        this.callHistory.push({ method: 'createFolder', name, parentToken });
        
        const folder = {
            token: TestUtils.generateUUID(),
            name,
            type: 'folder',
            parent_token: parentToken,
            created_time: new Date().toISOString(),
            modified_time: new Date().toISOString()
        };
        
        this.folders.set(folder.token, folder);
        
        return {
            code: 0,
            msg: 'success',
            data: folder
        };
    }
    
    async uploadFile(fileName, fileContent, parentToken = 'root', options = {}) {
        this.callHistory.push({ method: 'uploadFile', fileName, parentToken, options });
        
        const file = {
            token: TestUtils.generateUUID(),
            name: fileName,
            type: 'file',
            size: fileContent.length,
            parent_token: parentToken,
            mime_type: options.mimeType || 'application/octet-stream',
            created_time: new Date().toISOString(),
            modified_time: new Date().toISOString(),
            url: `https://lark.example.com/file/${TestUtils.generateUUID()}`
        };
        
        this.files.set(file.token, file);
        
        return {
            code: 0,
            msg: 'success',
            data: file
        };
    }
    
    async uploadFileByChunks(fileName, fileSize, parentToken = 'root', options = {}) {
        this.callHistory.push({ method: 'uploadFileByChunks', fileName, fileSize, parentToken, options });
        
        // Create upload session
        const sessionId = TestUtils.generateUUID();
        const session = {
            id: sessionId,
            fileName,
            fileSize,
            parentToken,
            chunks: [],
            status: 'active',
            created_at: new Date().toISOString()
        };
        
        this.uploadSessions.set(sessionId, session);
        
        return {
            code: 0,
            msg: 'success',
            data: {
                upload_id: sessionId,
                block_size: 8 * 1024 * 1024, // 8MB chunks
                block_num: Math.ceil(fileSize / (8 * 1024 * 1024))
            }
        };
    }
    
    async uploadChunk(uploadId, chunkIndex, chunkData) {
        this.callHistory.push({ method: 'uploadChunk', uploadId, chunkIndex });
        
        const session = this.uploadSessions.get(uploadId);
        if (!session) {
            return {
                code: 1,
                msg: 'Upload session not found'
            };
        }
        
        session.chunks[chunkIndex] = {
            index: chunkIndex,
            size: chunkData.length,
            uploaded_at: new Date().toISOString()
        };
        
        return {
            code: 0,
            msg: 'success',
            data: {
                chunk_index: chunkIndex,
                uploaded: true
            }
        };
    }
    
    async finishUpload(uploadId) {
        this.callHistory.push({ method: 'finishUpload', uploadId });
        
        const session = this.uploadSessions.get(uploadId);
        if (!session) {
            return {
                code: 1,
                msg: 'Upload session not found'
            };
        }
        
        const file = {
            token: TestUtils.generateUUID(),
            name: session.fileName,
            type: 'file',
            size: session.fileSize,
            parent_token: session.parentToken,
            created_time: new Date().toISOString(),
            modified_time: new Date().toISOString(),
            url: `https://lark.example.com/file/${TestUtils.generateUUID()}`
        };
        
        this.files.set(file.token, file);
        session.status = 'completed';
        session.file_token = file.token;
        
        return {
            code: 0,
            msg: 'success',
            data: file
        };
    }
    
    async getFileInfo(fileToken) {
        this.callHistory.push({ method: 'getFileInfo', fileToken });
        
        const file = this.files.get(fileToken);
        if (!file) {
            return {
                code: 1,
                msg: 'File not found'
            };
        }
        
        return {
            code: 0,
            msg: 'success',
            data: file
        };
    }
    
    async getFolderInfo(folderToken) {
        this.callHistory.push({ method: 'getFolderInfo', folderToken });
        
        const folder = this.folders.get(folderToken);
        if (!folder) {
            return {
                code: 1,
                msg: 'Folder not found'
            };
        }
        
        return {
            code: 0,
            msg: 'success',
            data: folder
        };
    }
    
    async listFolderContents(folderToken, pageSize = 50, pageToken = '') {
        this.callHistory.push({ method: 'listFolderContents', folderToken, pageSize, pageToken });
        
        const allItems = [
            ...Array.from(this.files.values()).filter(f => f.parent_token === folderToken),
            ...Array.from(this.folders.values()).filter(f => f.parent_token === folderToken)
        ];
        
        const startIndex = pageToken ? parseInt(pageToken) : 0;
        const endIndex = startIndex + pageSize;
        const items = allItems.slice(startIndex, endIndex);
        
        return {
            code: 0,
            msg: 'success',
            data: {
                items,
                has_more: endIndex < allItems.length,
                page_token: endIndex < allItems.length ? endIndex.toString() : ''
            }
        };
    }
    
    async deleteFile(fileToken) {
        this.callHistory.push({ method: 'deleteFile', fileToken });
        
        const deleted = this.files.delete(fileToken);
        
        return {
            code: deleted ? 0 : 1,
            msg: deleted ? 'success' : 'File not found'
        };
    }
    
    async deleteFolder(folderToken) {
        this.callHistory.push({ method: 'deleteFolder', folderToken });
        
        const deleted = this.folders.delete(folderToken);
        
        return {
            code: deleted ? 0 : 1,
            msg: deleted ? 'success' : 'Folder not found'
        };
    }
    
    // Utility methods cho testing
    addMockFile(file) {
        this.files.set(file.token, file);
    }
    
    addMockFolder(folder) {
        this.folders.set(folder.token, folder);
    }
    
    getCallHistory() {
        return [...this.callHistory];
    }
    
    clearCallHistory() {
        this.callHistory = [];
    }
    
    reset() {
        this.files.clear();
        this.folders.clear();
        this.uploadSessions.clear();
        this.callHistory = [];
        this.initializeMockData();
    }
    
    // Simulate API errors
    simulateError(method, error) {
        const originalMethod = this[method];
        this[method] = async (...args) => {
            throw error;
        };
        
        return () => {
            this[method] = originalMethod;
        };
    }
    
    // Simulate rate limiting
    simulateRateLimit(method, delayMs = 1000) {
        const originalMethod = this[method];
        this[method] = async (...args) => {
            await TestUtils.delay(delayMs);
            return originalMethod.apply(this, args);
        };
        
        return () => {
            this[method] = originalMethod;
        };
    }
    
    // Simulate network issues
    simulateNetworkError(method, errorRate = 0.5) {
        const originalMethod = this[method];
        this[method] = async (...args) => {
            if (Math.random() < errorRate) {
                throw new Error('Network error: Connection timeout');
            }
            return originalMethod.apply(this, args);
        };
        
        return () => {
            this[method] = originalMethod;
        };
    }
}

// Export singleton instance
export const larkDriveMock = new LarkDriveMock();

export default LarkDriveMock;
