/**
 * Unit Tests cho File Download Engine
 * Test tất cả functionality của file-download-engine.js
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import fs from 'fs';
import path from 'path';
import { FileDownloadEngine } from '../../src/services/file-download-engine.js';
import { TestUtils, TestAssertions, mockData } from '../test-config.js';
import { googleDriveMock } from '../mocks/google-drive-mock.js';

describe('FileDownloadEngine Service Tests', () => {
    let downloadEngine;
    let testUserEmail;
    let tempDir;
    
    beforeEach(() => {
        // Reset mocks
        googleDriveMock.reset();
        
        // Create download engine instance với mocked dependencies
        downloadEngine = new FileDownloadEngine();
        downloadEngine.driveAPI = googleDriveMock;
        
        testUserEmail = TestUtils.generateEmail();
        tempDir = './temp/test-downloads';
        downloadEngine.config.tempDir = tempDir;
        
        // Ensure temp directory exists
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
    });
    
    afterEach(async () => {
        googleDriveMock.clearCallHistory();
        
        // Cleanup temp files
        await downloadEngine.cleanup();
        
        // Remove temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    describe('Constructor và Configuration', () => {
        test('should initialize với default configuration', () => {
            const engine = new FileDownloadEngine();
            
            assert.strictEqual(engine.config.maxConcurrentDownloads, 5);
            assert.strictEqual(engine.config.chunkSize, 10 * 1024 * 1024);
            assert.strictEqual(engine.config.maxRetries, 3);
            assert.strictEqual(engine.config.retryDelay, 1000);
            assert(engine.config.supportedExportFormats);
            assert(typeof engine.config.supportedExportFormats === 'object');
        });
        
        test('should initialize stats correctly', () => {
            const engine = new FileDownloadEngine();
            
            assert.strictEqual(engine.stats.totalDownloads, 0);
            assert.strictEqual(engine.stats.successfulDownloads, 0);
            assert.strictEqual(engine.stats.failedDownloads, 0);
            assert.strictEqual(engine.stats.activeDownloads, 0);
            assert(Array.isArray(engine.stats.errors));
        });
        
        test('should initialize active downloads tracking', () => {
            const engine = new FileDownloadEngine();
            
            assert(engine.activeDownloads instanceof Map);
            assert.strictEqual(engine.activeDownloads.size, 0);
        });
    });

    describe('downloadFile Method', () => {
        test('should download binary file successfully', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-document.pdf',
                mimeType: 'application/pdf',
                size: '1024'
            };
            
            // Mock successful download
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadFile(testUserEmail, fileInfo);
            
            assert.strictEqual(result.success, true);
            assert.strictEqual(result.fileId, fileInfo.id);
            assert.strictEqual(result.fileName, fileInfo.name);
            assert(result.filePath);
            assert(result.downloadTime >= 0);
            TestAssertions.assertValidTimestamp(result.timestamp);
            
            // Verify file was created
            assert(fs.existsSync(result.filePath));
        });
        
        test('should download Google Doc successfully', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-document',
                mimeType: 'application/vnd.google-apps.document',
                size: null // Google Docs don't have size
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadFile(testUserEmail, fileInfo);
            
            assert.strictEqual(result.success, true);
            assert.strictEqual(result.fileId, fileInfo.id);
            assert(result.fileName.endsWith('.docx')); // Should add extension
            assert(result.filePath);
            assert(result.downloadTime >= 0);
            
            // Verify file was created
            assert(fs.existsSync(result.filePath));
        });
        
        test('should handle progress callback', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-file.txt',
                mimeType: 'text/plain',
                size: '2048'
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const progressUpdates = [];
            const progressCallback = (progress) => {
                progressUpdates.push(progress);
            };
            
            await downloadEngine.downloadFile(testUserEmail, fileInfo, progressCallback);
            
            // Should have received progress updates
            assert(progressUpdates.length > 0);
            
            // Verify progress structure
            const lastProgress = progressUpdates[progressUpdates.length - 1];
            TestAssertions.assertObjectHasKeys(lastProgress, [
                'fileId', 'fileName', 'downloadedBytes'
            ]);
        });
        
        test('should reject files that are too large', async () => {
            const largeFileInfo = {
                id: TestUtils.generateUUID(),
                name: 'huge-file.zip',
                mimeType: 'application/zip',
                size: (16 * 1024 * 1024 * 1024).toString() // 16GB
            };
            
            const result = await downloadEngine.downloadFile(testUserEmail, largeFileInfo);
            
            assert.strictEqual(result.success, false);
            assert(result.error.includes('File too large'));
        });
        
        test('should update statistics correctly', async () => {
            const fileInfo = TestUtils.createMockFile(1024);
            googleDriveMock.addMockFile(fileInfo);
            
            const initialStats = { ...downloadEngine.stats };
            
            await downloadEngine.downloadFile(testUserEmail, fileInfo);
            
            assert.strictEqual(downloadEngine.stats.totalDownloads, initialStats.totalDownloads + 1);
            assert.strictEqual(downloadEngine.stats.successfulDownloads, initialStats.successfulDownloads + 1);
        });
    });

    describe('downloadGoogleDoc Method', () => {
        test('should export Google Doc to DOCX', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-document',
                mimeType: 'application/vnd.google-apps.document'
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadGoogleDoc(testUserEmail, fileInfo);
            
            assert(result.filePath);
            assert(result.filePath.endsWith('.docx'));
            assert.strictEqual(result.mimeType, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            assert(typeof result.size === 'number');
            assert(typeof result.downloadTime === 'number');
            assert(result.checksum);
        });
        
        test('should export Google Sheets to XLSX', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-spreadsheet',
                mimeType: 'application/vnd.google-apps.spreadsheet'
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadGoogleDoc(testUserEmail, fileInfo);
            
            assert(result.filePath.endsWith('.xlsx'));
            assert.strictEqual(result.mimeType, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        });
        
        test('should export Google Slides to PPTX', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-presentation',
                mimeType: 'application/vnd.google-apps.presentation'
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadGoogleDoc(testUserEmail, fileInfo);
            
            assert(result.filePath.endsWith('.pptx'));
            assert.strictEqual(result.mimeType, 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
        });
        
        test('should handle unsupported Google Apps format', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-unsupported',
                mimeType: 'application/vnd.google-apps.unknown'
            };
            
            await assert.rejects(
                downloadEngine.downloadGoogleDoc(testUserEmail, fileInfo),
                /Unsupported Google Apps format/
            );
        });
        
        test('should cleanup partial file on error', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-document',
                mimeType: 'application/vnd.google-apps.document'
            };
            
            // Simulate export error
            const restoreError = googleDriveMock.simulateError(
                'exportFile',
                new Error('Export failed')
            );
            
            try {
                await assert.rejects(
                    downloadEngine.downloadGoogleDoc(testUserEmail, fileInfo),
                    /Google Docs export failed/
                );
                
                // Verify partial file was cleaned up
                const expectedPath = path.join(tempDir, `${fileInfo.id}_${fileInfo.name}.docx`);
                assert(!fs.existsSync(expectedPath));
            } finally {
                restoreError();
            }
        });
    });

    describe('downloadBinaryFile Method', () => {
        test('should download binary file với progress tracking', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-image.jpg',
                mimeType: 'image/jpeg',
                size: '2048'
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const progressUpdates = [];
            const progressCallback = (progress) => {
                progressUpdates.push(progress);
            };
            
            const result = await downloadEngine.downloadBinaryFile(testUserEmail, fileInfo, progressCallback);
            
            assert(result.filePath);
            assert.strictEqual(result.mimeType, fileInfo.mimeType);
            assert(typeof result.size === 'number');
            assert(typeof result.downloadTime === 'number');
            assert(result.checksum);
            
            // Verify progress was tracked
            assert(progressUpdates.length > 0);
            
            const lastProgress = progressUpdates[progressUpdates.length - 1];
            assert.strictEqual(lastProgress.fileId, fileInfo.id);
            assert.strictEqual(lastProgress.fileName, fileInfo.name);
            assert(typeof lastProgress.downloadedBytes === 'number');
            assert(typeof lastProgress.totalBytes === 'number');
        });
        
        test('should handle files without size information', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-file.bin',
                mimeType: 'application/octet-stream',
                size: null
            };
            
            googleDriveMock.addMockFile(fileInfo);
            
            const result = await downloadEngine.downloadBinaryFile(testUserEmail, fileInfo);
            
            assert(result.filePath);
            assert(typeof result.size === 'number');
        });
        
        test('should cleanup partial file on error', async () => {
            const fileInfo = {
                id: TestUtils.generateUUID(),
                name: 'test-file.pdf',
                mimeType: 'application/pdf',
                size: '1024'
            };
            
            // Simulate download error
            const restoreError = googleDriveMock.simulateError(
                'downloadFile',
                new Error('Download failed')
            );
            
            try {
                await assert.rejects(
                    downloadEngine.downloadBinaryFile(testUserEmail, fileInfo),
                    /Binary file download failed/
                );
                
                // Verify partial file was cleaned up
                const expectedPath = path.join(tempDir, `${fileInfo.id}_${fileInfo.name}`);
                assert(!fs.existsSync(expectedPath));
            } finally {
                restoreError();
            }
        });
    });

    describe('Utility Methods', () => {
        test('should calculate file checksum correctly', async () => {
            // Create a test file
            const testFilePath = path.join(tempDir, 'test-checksum.txt');
            const testContent = 'Hello, World!';
            fs.writeFileSync(testFilePath, testContent);
            
            const checksum = await downloadEngine.calculateChecksum(testFilePath);
            
            assert(typeof checksum === 'string');
            assert(checksum.length > 0);
            
            // Calculate again to verify consistency
            const checksum2 = await downloadEngine.calculateChecksum(testFilePath);
            assert.strictEqual(checksum, checksum2);
        });
        
        test('should get correct file extension for Google Docs', () => {
            const testCases = [
                { mimeType: 'application/vnd.google-apps.document', expected: '.docx' },
                { mimeType: 'application/vnd.google-apps.spreadsheet', expected: '.xlsx' },
                { mimeType: 'application/vnd.google-apps.presentation', expected: '.pptx' },
                { mimeType: 'application/vnd.google-apps.drawing', expected: '.png' }
            ];
            
            testCases.forEach(({ mimeType, expected }) => {
                const extension = downloadEngine.getFileExtension(mimeType);
                assert.strictEqual(extension, expected);
            });
        });
        
        test('should return stats correctly', () => {
            const stats = downloadEngine.getStats();
            
            TestAssertions.assertObjectHasKeys(stats, [
                'totalDownloads',
                'successfulDownloads',
                'failedDownloads',
                'activeDownloads',
                'errors'
            ]);
            
            assert(typeof stats.totalDownloads === 'number');
            assert(typeof stats.successfulDownloads === 'number');
            assert(typeof stats.failedDownloads === 'number');
            assert(typeof stats.activeDownloads === 'number');
            assert(Array.isArray(stats.errors));
        });
        
        test('should reset stats correctly', () => {
            // Add some stats
            downloadEngine.stats.totalDownloads = 5;
            downloadEngine.stats.successfulDownloads = 3;
            downloadEngine.stats.failedDownloads = 2;
            downloadEngine.stats.errors.push({ error: 'test error' });
            
            downloadEngine.resetStats();
            
            assert.strictEqual(downloadEngine.stats.totalDownloads, 0);
            assert.strictEqual(downloadEngine.stats.successfulDownloads, 0);
            assert.strictEqual(downloadEngine.stats.failedDownloads, 0);
            assert.strictEqual(downloadEngine.stats.errors.length, 0);
        });
    });

    describe('Error Handling', () => {
        test('should handle Google Drive API errors', async () => {
            const fileInfo = TestUtils.createMockFile(1024);
            
            // Simulate API error
            const restoreError = googleDriveMock.simulateError(
                'downloadFile',
                new Error('API quota exceeded')
            );
            
            try {
                const result = await downloadEngine.downloadFile(testUserEmail, fileInfo);
                
                assert.strictEqual(result.success, false);
                assert(result.error.includes('API quota exceeded'));
                assert.strictEqual(downloadEngine.stats.failedDownloads, 1);
            } finally {
                restoreError();
            }
        });
        
        test('should handle network timeouts', async () => {
            const fileInfo = TestUtils.createMockFile(1024);
            
            // Simulate network error
            const restoreError = googleDriveMock.simulateNetworkError('downloadFile', 1.0);
            
            try {
                const result = await downloadEngine.downloadFile(testUserEmail, fileInfo);
                
                assert.strictEqual(result.success, false);
                assert(result.error.includes('Network error'));
            } finally {
                restoreError();
            }
        });
        
        test('should track errors in stats', async () => {
            const fileInfo = TestUtils.createMockFile(1024);
            
            const restoreError = googleDriveMock.simulateError(
                'downloadFile',
                new Error('Test error')
            );
            
            try {
                await downloadEngine.downloadFile(testUserEmail, fileInfo);
                
                assert.strictEqual(downloadEngine.stats.errors.length, 1);
                
                const error = downloadEngine.stats.errors[0];
                assert.strictEqual(error.fileId, fileInfo.id);
                assert.strictEqual(error.fileName, fileInfo.name);
                assert.strictEqual(error.error, 'Test error');
                TestAssertions.assertValidTimestamp(error.timestamp);
            } finally {
                restoreError();
            }
        });
    });

    describe('Cleanup Operations', () => {
        test('should cleanup specific files', async () => {
            // Create test files
            const file1 = path.join(tempDir, 'test1.txt');
            const file2 = path.join(tempDir, 'test2.txt');
            
            fs.writeFileSync(file1, 'content1');
            fs.writeFileSync(file2, 'content2');
            
            assert(fs.existsSync(file1));
            assert(fs.existsSync(file2));
            
            // Cleanup specific file
            await downloadEngine.cleanup([file1]);
            
            assert(!fs.existsSync(file1));
            assert(fs.existsSync(file2)); // Should still exist
        });
        
        test('should cleanup all temp files', async () => {
            // Create test files
            const file1 = path.join(tempDir, 'test1.txt');
            const file2 = path.join(tempDir, 'test2.txt');
            
            fs.writeFileSync(file1, 'content1');
            fs.writeFileSync(file2, 'content2');
            
            assert(fs.existsSync(file1));
            assert(fs.existsSync(file2));
            
            // Cleanup all files
            await downloadEngine.cleanup();
            
            assert(!fs.existsSync(file1));
            assert(!fs.existsSync(file2));
        });
        
        test('should handle cleanup errors gracefully', async () => {
            // Try to cleanup non-existent file
            await downloadEngine.cleanup(['/non/existent/file.txt']);
            
            // Should not throw error
            assert(true, 'Should handle cleanup errors gracefully');
        });
    });

    describe('Concurrent Downloads', () => {
        test('should track active downloads', async () => {
            const fileInfo = TestUtils.createMockFile(1024);
            googleDriveMock.addMockFile(fileInfo);
            
            assert.strictEqual(downloadEngine.stats.activeDownloads, 0);
            
            const downloadPromise = downloadEngine.downloadFile(testUserEmail, fileInfo);
            
            // During download, active count should be 1
            // Note: This is a simplified test, in real scenario we'd need to check during actual download
            
            await downloadPromise;
            
            // After download, active count should be back to 0
            assert.strictEqual(downloadEngine.stats.activeDownloads, 0);
        });
        
        test('should respect concurrent download limits', () => {
            assert.strictEqual(downloadEngine.config.maxConcurrentDownloads, 5);
            
            // In a real implementation, we would test that no more than 5 downloads
            // happen simultaneously, but this requires more complex async testing
            assert(true, 'Concurrent limit configuration is correct');
        });
    });
});
