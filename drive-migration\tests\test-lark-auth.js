import { larkAuth } from './auth/lark-auth.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test Lark Authentication and API access
 */
async function testLarkAuth() {
    console.log('🔍 Testing Lark Authentication...\n');
    console.log('=' .repeat(60));

    try {
        // 1. Validate credentials
        console.log('\n1. Validating Lark App credentials...');
        const isValid = larkAuth.validateCredentials();
        
        if (isValid) {
            console.log('✅ Lark App credentials format is valid');
            console.log(`📱 App ID: ${larkAuth.appId}`);
            console.log(`🔗 Base URL: ${larkAuth.baseUrl}`);
        } else {
            console.log('❌ Lark App credentials are invalid');
            console.log('💡 Check LARK_APP_ID and LARK_APP_SECRET in .env file');
            return;
        }

        // 2. Test token acquisition
        console.log('\n2. Testing token acquisition...');
        const startTime = Date.now();
        
        try {
            const token = await larkAuth.getTenantAccessToken();
            const tokenTime = Date.now() - startTime;
            
            console.log('✅ Tenant Access Token acquired successfully');
            console.log(`⏱️ Token acquisition time: ${tokenTime}ms`);
            console.log(`🎫 Token length: ${token.length} characters`);
            
            // Test token info
            const tokenInfo = larkAuth.getTokenInfo();
            console.log(`⏰ Token expires at: ${tokenInfo.expiresAt}`);
            console.log(`⏳ Time to expiry: ${tokenInfo.timeToExpiryFormatted}`);
            
        } catch (tokenError) {
            console.log('❌ Token acquisition failed:', tokenError.message);
            
            if (tokenError.message.includes('Invalid App ID')) {
                console.log('💡 Tip: Verify your LARK_APP_ID is correct');
            } else if (tokenError.message.includes('Invalid App Secret')) {
                console.log('💡 Tip: Verify your LARK_APP_SECRET is correct');
            }
            return;
        }

        // 3. Test caching
        console.log('\n3. Testing token caching...');
        const startTime2 = Date.now();
        await larkAuth.getTenantAccessToken(); // Should use cache
        const cachedTime = Date.now() - startTime2;
        
        console.log(`⏱️ Cached token time: ${cachedTime}ms`);
        console.log(`🚀 Cache speedup: ${Math.round(tokenTime / cachedTime)}x faster`);

        // 4. Test connection
        console.log('\n4. Testing API connection...');
        const connectionResult = await larkAuth.testConnection();
        
        if (connectionResult.success) {
            console.log('✅ Lark API connection successful!');
            
            if (connectionResult.appInfo) {
                console.log(`📱 App Name: ${connectionResult.appInfo.app_name}`);
                console.log(`🏢 App Type: ${connectionResult.appInfo.app_type}`);
                console.log(`📊 Status: ${connectionResult.appInfo.status}`);
                console.log(`🆔 App ID: ${connectionResult.appInfo.app_id}`);
            }
            
            console.log(`🔑 Available permissions: ${connectionResult.permissions.join(', ')}`);
            console.log(`⏱️ Performance: Auth ${connectionResult.performance.authTime}ms, Total ${connectionResult.performance.totalTime}ms`);
            
        } else {
            console.log('❌ Lark API connection failed');
            connectionResult.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });
        }

        // 5. Test Drive operations
        console.log('\n5. Testing Lark Drive operations...');
        const driveResult = await larkAuth.testDriveOperations();
        
        if (driveResult.success) {
            console.log('✅ Lark Drive operations accessible');
            console.log(`🔧 Available operations: ${driveResult.operations.join(', ')}`);
        } else {
            console.log('⚠️ Some Lark Drive operations not accessible');
            driveResult.errors.forEach(error => {
                console.log(`   ⚠️ ${error}`);
            });
        }

        // 6. Validate app setup
        console.log('\n6. Validating complete app setup...');
        const validation = await larkAuth.validateAppSetup();
        
        if (validation.success) {
            console.log('✅ Lark app setup is complete and ready for migration!');
        } else {
            console.log('⚠️ Lark app setup needs attention:');
            validation.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });
            
            if (validation.recommendations.length > 0) {
                console.log('\n📋 Recommendations:');
                validation.recommendations.forEach(rec => {
                    console.log(`   💡 ${rec}`);
                });
            }
        }

        // 7. Get app permissions
        console.log('\n7. Checking app permissions...');
        const permissionsResult = await larkAuth.getAppPermissions();
        
        if (permissionsResult.success) {
            console.log('✅ App permissions retrieved:');
            if (permissionsResult.permissions.length > 0) {
                permissionsResult.permissions.forEach(scope => {
                    console.log(`   🔑 ${scope}`);
                });
            } else {
                console.log('   ⚠️ No specific permissions listed (may be using default scopes)');
            }
        } else {
            console.log('⚠️ Could not retrieve app permissions:', permissionsResult.error);
        }

        // 8. Cache statistics
        console.log('\n8. Cache statistics...');
        const cacheStats = larkAuth.getCacheStats();
        console.log(`📊 Token cached: ${cacheStats.hasToken}`);
        console.log(`🕐 Token expiry: ${cacheStats.tokenExpiry ? new Date(cacheStats.tokenExpiry) : 'N/A'}`);
        console.log(`📱 Cached clients: ${cacheStats.clientsCount}`);
        console.log(`🚦 Rate limiter: ${cacheStats.rateLimiter.remaining}/${larkAuth.rateLimiter.maxRequests} requests remaining`);

        // 9. Test error handling
        console.log('\n9. Testing error handling...');
        
        // Test with invalid token
        larkAuth.invalidateToken();
        const originalAppSecret = larkAuth.appSecret;
        larkAuth.appSecret = 'invalid_secret';
        
        try {
            await larkAuth.getTenantAccessToken();
            console.log('❌ Error handling test failed - should have thrown error');
        } catch (error) {
            console.log('✅ Error handling works correctly for invalid credentials');
        }
        
        // Restore original secret
        larkAuth.appSecret = originalAppSecret;

        console.log('\n🎉 Lark Auth testing completed!');

    } catch (error) {
        console.error('❌ Lark Auth test failed:', error);
        
        // Provide helpful error messages
        if (error.message.includes('ENOTFOUND')) {
            console.log('\n💡 Tip: Check internet connection to reach Lark API');
        } else if (error.message.includes('timeout')) {
            console.log('\n💡 Tip: Lark API request timed out, try again');
        }
    }
}

/**
 * Test rate limiting
 */
async function testRateLimiting() {
    console.log('\n🔍 Testing Rate Limiting...\n');
    
    try {
        console.log('Making multiple rapid requests to test rate limiting...');
        
        const promises = [];
        for (let i = 0; i < 5; i++) {
            promises.push(larkAuth.getTenantAccessToken());
        }
        
        await Promise.all(promises);
        console.log('✅ Rate limiting allows normal usage patterns');
        
        const cacheStats = larkAuth.getCacheStats();
        console.log(`📊 Requests made: ${cacheStats.rateLimiter.requests || 0}`);
        console.log(`⏰ Reset time: ${cacheStats.rateLimiter.resetTime}`);
        
    } catch (error) {
        if (error.message.includes('Rate limit')) {
            console.log('✅ Rate limiting is working correctly');
        } else {
            console.log('❌ Rate limiting test failed:', error.message);
        }
    }
}

/**
 * Performance benchmark
 */
async function benchmarkPerformance() {
    console.log('\n🔍 Performance Benchmark...\n');
    
    try {
        // Clear cache for fair test
        larkAuth.clearCache();
        
        // Test 1: Cold start (no cache)
        console.log('1. Cold start performance...');
        const coldStart = Date.now();
        await larkAuth.getTenantAccessToken();
        const coldTime = Date.now() - coldStart;
        console.log(`❄️ Cold start: ${coldTime}ms`);
        
        // Test 2: Warm cache
        console.log('2. Warm cache performance...');
        const warmTimes = [];
        for (let i = 0; i < 5; i++) {
            const start = Date.now();
            await larkAuth.getTenantAccessToken();
            warmTimes.push(Date.now() - start);
        }
        
        const avgWarmTime = warmTimes.reduce((a, b) => a + b, 0) / warmTimes.length;
        console.log(`🔥 Warm cache average: ${avgWarmTime.toFixed(2)}ms`);
        console.log(`🚀 Cache improvement: ${(coldTime / avgWarmTime).toFixed(1)}x faster`);
        
        // Test 3: Client creation
        console.log('3. Client creation performance...');
        const clientStart = Date.now();
        await larkAuth.getAuthenticatedClient('benchmark');
        const clientTime = Date.now() - clientStart;
        console.log(`🔧 Client creation: ${clientTime}ms`);
        
    } catch (error) {
        console.error('❌ Performance benchmark failed:', error.message);
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Lark Authentication Tests\n');
    
    await testLarkAuth();
    await testRateLimiting();
    await benchmarkPerformance();
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ All Lark Auth tests completed!');
    
    // Show final cache stats
    const finalStats = larkAuth.getCacheStats();
    console.log(`\n📊 Final stats: Token cached: ${finalStats.hasToken}, Clients: ${finalStats.clientsCount}`);
    
    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testLarkAuth, testRateLimiting, benchmarkPerformance };
