import express from 'express';
const router = express.Router();

/**
 * Test Error Routes
 * Các endpoint để test các loại lỗi khác nhau
 */

// Test 400 Bad Request
router.post('/400', (req, res) => {
  res.status(400).json({
    message: 'Dữ liệu yêu cầu không hợp lệ',
    details: 'Thiếu trường bắt buộc hoặc định dạng không đúng',
    code: 'INVALID_REQUEST_DATA'
  });
});

// Test 401 Unauthorized
router.get('/401', (req, res) => {
  res.status(401).json({
    message: 'Không có quyền truy cập',
    details: 'Token xác thực không hợp lệ hoặc đã hết hạn',
    code: 'UNAUTHORIZED'
  });
});

// Test 403 Forbidden
router.get('/403', (req, res) => {
  res.status(403).json({
    message: '<PERSON><PERSON> cấm truy cập tài nguyên này',
    details: 'Người dùng không có quyền thực hiện hành động này',
    code: 'FORBIDDEN'
  });
});

// Test 404 Not Found
router.get('/404', (req, res) => {
  res.status(404).json({
    message: 'Không tìm thấy tài nguyên',
    details: 'Endpoint hoặc tài nguyên được yêu cầu không tồn tại',
    code: 'NOT_FOUND'
  });
});

// Test 429 Too Many Requests
router.get('/429', (req, res) => {
  res.status(429).json({
    message: 'Quá nhiều yêu cầu',
    details: 'Vui lòng thử lại sau 60 giây',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: 60
  });
});

// Test 500 Internal Server Error
router.post('/500', (req, res) => {
  res.status(500).json({
    message: 'Lỗi máy chủ nội bộ',
    details: 'Đã xảy ra lỗi không mong muốn trên máy chủ',
    code: 'INTERNAL_SERVER_ERROR'
  });
});

// Test 502 Bad Gateway
router.get('/502', (req, res) => {
  res.status(502).json({
    message: 'Lỗi gateway',
    details: 'Máy chủ upstream trả về phản hồi không hợp lệ',
    code: 'BAD_GATEWAY'
  });
});

// Test 503 Service Unavailable
router.get('/503', (req, res) => {
  res.status(503).json({
    message: 'Dịch vụ không khả dụng',
    details: 'Máy chủ tạm thời không thể xử lý yêu cầu',
    code: 'SERVICE_UNAVAILABLE'
  });
});

// Test 504 Gateway Timeout
router.get('/504', (req, res) => {
  res.status(504).json({
    message: 'Hết thời gian chờ gateway',
    details: 'Máy chủ upstream không phản hồi trong thời gian cho phép',
    code: 'GATEWAY_TIMEOUT'
  });
});

// Test validation error
router.post('/validation', (req, res) => {
  const errors = [];

  if (!req.body.email) {
    errors.push({ field: 'email', message: 'Email là bắt buộc' });
  } else if (!/\S+@\S+\.\S+/.test(req.body.email)) {
    errors.push({ field: 'email', message: 'Email không hợp lệ' });
  }

  if (!req.body.password) {
    errors.push({ field: 'password', message: 'Mật khẩu là bắt buộc' });
  } else if (req.body.password.length < 6) {
    errors.push({ field: 'password', message: 'Mật khẩu phải có ít nhất 6 ký tự' });
  }

  if (errors.length > 0) {
    return res.status(400).json({
      message: 'Dữ liệu validation không hợp lệ',
      details: 'Một hoặc nhiều trường không đáp ứng yêu cầu',
      code: 'VALIDATION_ERROR',
      errors: errors
    });
  }

  res.json({ message: 'Validation thành công' });
});

// Test timeout (delayed response)
router.get('/timeout', (req, res) => {
  const delay = parseInt(req.query.delay) || 10000; // Default 10 seconds

  setTimeout(() => {
    res.json({
      message: 'Phản hồi sau khi delay',
      delay: delay
    });
  }, delay);
});

// Test random error
router.get('/random', (req, res) => {
  const errors = [400, 401, 403, 404, 429, 500, 502, 503, 504];
  const randomStatus = errors[Math.floor(Math.random() * errors.length)];

  const messages = {
    400: 'Lỗi ngẫu nhiên: Yêu cầu không hợp lệ',
    401: 'Lỗi ngẫu nhiên: Không có quyền truy cập',
    403: 'Lỗi ngẫu nhiên: Bị cấm truy cập',
    404: 'Lỗi ngẫu nhiên: Không tìm thấy',
    429: 'Lỗi ngẫu nhiên: Quá nhiều yêu cầu',
    500: 'Lỗi ngẫu nhiên: Lỗi máy chủ',
    502: 'Lỗi ngẫu nhiên: Lỗi gateway',
    503: 'Lỗi ngẫu nhiên: Dịch vụ không khả dụng',
    504: 'Lỗi ngẫu nhiên: Hết thời gian chờ'
  };

  res.status(randomStatus).json({
    message: messages[randomStatus],
    details: `Lỗi được tạo ngẫu nhiên với status ${randomStatus}`,
    code: 'RANDOM_ERROR'
  });
});

// Test success response
router.get('/success', (req, res) => {
  res.json({
    message: 'Test thành công!',
    timestamp: new Date().toISOString(),
    data: {
      status: 'ok',
      version: '1.0.0'
    }
  });
});

export default router;
