/**
 * Unit Tests cho Drive Scanner Service
 * Test tất cả functionality của drive-scanner.js
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { DriveScanner } from '../../src/services/drive-scanner.js';
import { TestUtils, TestAssertions, mockData } from '../test-config.js';
import { googleDriveMock } from '../mocks/google-drive-mock.js';
import { databaseMock } from '../mocks/database-mock.js';

describe('DriveScanner Service Tests', () => {
    let driveScanner;
    let testUserEmail;
    
    beforeEach(() => {
        // Reset mocks
        googleDriveMock.reset();
        databaseMock.reset();
        
        // Create scanner instance với mocked dependencies
        driveScanner = new DriveScanner();
        driveScanner.driveAPI = googleDriveMock;
        driveScanner.supabase = databaseMock;
        
        testUserEmail = TestUtils.generateEmail();
    });
    
    afterEach(() => {
        googleDriveMock.clearCallHistory();
        databaseMock.clearCallHistory();
    });

    describe('Constructor và Configuration', () => {
        test('should initialize với default configuration', () => {
            const scanner = new DriveScanner();
            
            assert.strictEqual(scanner.config.maxDepth, 100);
            assert.strictEqual(scanner.config.batchSize, 100);
            assert.strictEqual(scanner.config.maxConcurrentRequests, 5);
            assert(Array.isArray(scanner.config.supportedMimeTypes));
            assert(scanner.config.supportedMimeTypes.length > 0);
        });
        
        test('should initialize scan state correctly', () => {
            const scanner = new DriveScanner();
            
            assert.strictEqual(scanner.scanState.isScanning, false);
            assert.strictEqual(scanner.scanState.totalFiles, 0);
            assert.strictEqual(scanner.scanState.scannedFiles, 0);
            assert.strictEqual(scanner.scanState.currentDepth, 0);
            assert(Array.isArray(scanner.scanState.errors));
        });
    });

    describe('startFullScan Method', () => {
        test('should start full scan successfully', async () => {
            const options = {
                maxDepth: 5,
                includeSharedDrives: true
            };
            
            const result = await driveScanner.startFullScan(testUserEmail, options);
            
            // Verify result structure
            TestAssertions.assertObjectHasKeys(result, ['sessionId', 'totalFiles', 'totalSize']);
            TestAssertions.assertValidUUID(result.sessionId);
            assert(typeof result.totalFiles === 'number');
            assert(typeof result.totalSize === 'number');
            
            // Verify scan state
            assert.strictEqual(driveScanner.scanState.isScanning, false);
            
            // Verify database calls
            const dbHistory = databaseMock.getCallHistory();
            const insertCalls = dbHistory.filter(call => call.method === 'insert');
            assert(insertCalls.length > 0, 'Should create scan session');
        });
        
        test('should handle scan options correctly', async () => {
            const customOptions = {
                maxDepth: 10,
                filterMimeTypes: ['application/pdf'],
                includeTrash: false
            };
            
            await driveScanner.startFullScan(testUserEmail, customOptions);
            
            // Verify options were applied
            const dbHistory = databaseMock.getCallHistory();
            const sessionInsert = dbHistory.find(call => 
                call.method === 'insert' && call.tableName === 'scan_sessions'
            );
            
            assert(sessionInsert);
            assert.deepStrictEqual(
                sessionInsert.data.scan_options.filterMimeTypes,
                customOptions.filterMimeTypes
            );
        });
        
        test('should handle scan errors gracefully', async () => {
            // Simulate database error
            const restoreError = databaseMock.simulateError('insert', new Error('Database connection failed'));
            
            try {
                await assert.rejects(
                    driveScanner.startFullScan(testUserEmail),
                    /Failed to create scan session/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should prevent concurrent scans', async () => {
            // Start first scan
            driveScanner.scanState.isScanning = true;
            
            await assert.rejects(
                driveScanner.startFullScan(testUserEmail),
                /Scan already in progress/
            );
        });
    });

    describe('scanFromRoot Method', () => {
        test('should scan from root folder successfully', async () => {
            const sessionId = TestUtils.generateUUID();
            const options = { maxDepth: 3 };
            
            // Add mock files to root
            const mockFile = TestUtils.createMockFile(1024);
            const mockFolder = TestUtils.createMockFolder();
            googleDriveMock.addMockFile(mockFile);
            googleDriveMock.addMockFolder(mockFolder);
            
            const result = await driveScanner.scanFromRoot(testUserEmail, options, sessionId);
            
            assert(typeof result.totalFiles === 'number');
            assert(typeof result.totalSize === 'number');
            assert(result.totalFiles >= 0);
            assert(result.totalSize >= 0);
        });
        
        test('should respect maxDepth limit', async () => {
            const sessionId = TestUtils.generateUUID();
            const options = { maxDepth: 1 };
            
            // Create nested folder structure
            const rootFolder = TestUtils.createMockFolder();
            rootFolder.id = 'root';
            const childFolder = TestUtils.createMockFolder();
            childFolder.parents = [rootFolder.id];
            
            googleDriveMock.addMockFolder(rootFolder);
            googleDriveMock.addMockFolder(childFolder);
            
            await driveScanner.scanFromRoot(testUserEmail, options, sessionId);
            
            // Verify depth was respected
            assert(driveScanner.scanState.currentDepth <= options.maxDepth);
        });
        
        test('should handle empty folders', async () => {
            const sessionId = TestUtils.generateUUID();
            const options = {};
            
            // Clear all mock data để simulate empty drive
            googleDriveMock.reset();
            googleDriveMock.folders.clear();
            googleDriveMock.files.clear();
            
            const result = await driveScanner.scanFromRoot(testUserEmail, options, sessionId);
            
            assert.strictEqual(result.totalFiles, 0);
            assert.strictEqual(result.totalSize, 0);
        });
    });

    describe('scanFolder Method', () => {
        test('should scan folder contents successfully', async () => {
            const folder = {
                id: 'test-folder-id',
                path: '/test-folder',
                depth: 1
            };
            
            // Add mock files to folder
            const mockFile = TestUtils.createMockFile(2048);
            mockFile.parents = [folder.id];
            googleDriveMock.addMockFile(mockFile);
            
            const files = await driveScanner.scanFolder(testUserEmail, folder, {});
            
            assert(Array.isArray(files));
            assert(files.length >= 0);
            
            // Verify Google Drive API was called
            const apiHistory = googleDriveMock.getCallHistory();
            const listCalls = apiHistory.filter(call => call.method === 'listFiles');
            assert(listCalls.length > 0);
        });
        
        test('should handle root folder scanning', async () => {
            const rootFolder = {
                id: 'root',
                path: '/',
                depth: 0
            };
            
            const files = await driveScanner.scanFolder(testUserEmail, rootFolder, {});
            
            assert(Array.isArray(files));
            
            // Verify correct query was used for root
            const apiHistory = googleDriveMock.getCallHistory();
            const listCall = apiHistory.find(call => call.method === 'listFiles');
            assert(listCall.options.q.includes("'root' in parents"));
        });
        
        test('should apply mime type filters', async () => {
            const folder = { id: 'test-folder', path: '/test', depth: 1 };
            const options = {
                filterMimeTypes: ['application/pdf']
            };
            
            await driveScanner.scanFolder(testUserEmail, folder, options);
            
            // Verify filter was applied in query
            const apiHistory = googleDriveMock.getCallHistory();
            const listCall = apiHistory.find(call => call.method === 'listFiles');
            assert(listCall.options.q.includes('trashed=false'));
        });
    });

    describe('Database Operations', () => {
        test('should create scan session correctly', async () => {
            const options = {
                maxDepth: 5,
                includeSharedDrives: true
            };
            
            const session = await driveScanner.createScanSession(testUserEmail, options);
            
            TestAssertions.assertValidUUID(session.id);
            assert.strictEqual(session.user_email, testUserEmail);
            assert.strictEqual(session.scan_type, 'full_drive');
            assert.strictEqual(session.status, 'running');
            TestAssertions.assertValidTimestamp(session.started_at);
        });
        
        test('should update scan session correctly', async () => {
            const sessionId = TestUtils.generateUUID();
            const updates = {
                status: 'completed',
                total_files: 100,
                scan_duration: 5000
            };
            
            await driveScanner.updateScanSession(sessionId, updates);
            
            const dbHistory = databaseMock.getCallHistory();
            const updateCall = dbHistory.find(call => 
                call.method === 'update' && call.tableName === 'scan_sessions'
            );
            
            assert(updateCall);
            assert.deepStrictEqual(updateCall.data, updates);
        });
        
        test('should store scanned file correctly', async () => {
            const sessionId = TestUtils.generateUUID();
            const file = {
                id: TestUtils.generateUUID(),
                name: 'test-file.pdf',
                mimeType: 'application/pdf',
                size: '1024',
                fullPath: '/test-folder/test-file.pdf',
                depth: 1,
                parents: ['parent-folder-id'],
                createdTime: new Date().toISOString(),
                modifiedTime: new Date().toISOString(),
                webViewLink: 'https://drive.google.com/file/d/test/view'
            };
            
            await driveScanner.storeScannedFile(file, sessionId);
            
            const dbHistory = databaseMock.getCallHistory();
            const insertCall = dbHistory.find(call => 
                call.method === 'insert' && call.tableName === 'scanned_files'
            );
            
            assert(insertCall);
            assert.strictEqual(insertCall.data.scan_session_id, sessionId);
            assert.strictEqual(insertCall.data.file_id, file.id);
            assert.strictEqual(insertCall.data.name, file.name);
            assert.strictEqual(insertCall.data.size, 1024);
        });
    });

    describe('Utility Methods', () => {
        test('should get scan status correctly', () => {
            driveScanner.scanState.isScanning = true;
            driveScanner.scanState.totalFiles = 50;
            driveScanner.scanState.scannedFiles = 25;
            
            const status = driveScanner.getScanStatus();
            
            assert.strictEqual(status.isScanning, true);
            assert.strictEqual(status.totalFiles, 50);
            assert.strictEqual(status.scannedFiles, 25);
            assert.strictEqual(status.progress, 50); // 25/50 * 100
        });
        
        test('should build file path correctly', () => {
            const parentPath = '/folder1/folder2';
            const fileName = 'document.pdf';
            
            const fullPath = driveScanner.buildFilePath(parentPath, fileName);
            
            assert.strictEqual(fullPath, '/folder1/folder2/document.pdf');
        });
        
        test('should handle root path correctly', () => {
            const parentPath = '/';
            const fileName = 'root-file.txt';
            
            const fullPath = driveScanner.buildFilePath(parentPath, fileName);
            
            assert.strictEqual(fullPath, '/root-file.txt');
        });
        
        test('should stop scan correctly', () => {
            driveScanner.scanState.isScanning = true;
            
            driveScanner.stopScan();
            
            assert.strictEqual(driveScanner.scanState.isScanning, false);
        });
    });

    describe('Error Handling', () => {
        test('should handle Google Drive API errors', async () => {
            const folder = { id: 'test-folder', path: '/test', depth: 1 };
            
            // Simulate API error
            const restoreError = googleDriveMock.simulateError(
                'listFiles', 
                new Error('API quota exceeded')
            );
            
            try {
                await assert.rejects(
                    driveScanner.scanFolder(testUserEmail, folder, {}),
                    /API quota exceeded/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should handle database errors gracefully', async () => {
            const sessionId = TestUtils.generateUUID();
            const file = TestUtils.createMockFile();
            
            // Simulate database error
            const restoreError = databaseMock.simulateError(
                'insert',
                new Error('Database connection lost')
            );
            
            try {
                // Should not throw, just log error
                await driveScanner.storeScannedFile(file, sessionId);
                
                // Verify error was logged (check console output in real implementation)
                assert(true, 'Should handle database errors gracefully');
            } finally {
                restoreError();
            }
        });
        
        test('should track errors in scan state', async () => {
            driveScanner.scanState.errors = [];
            
            // Simulate error during scanning
            const error = new Error('Test error');
            driveScanner.scanState.errors.push({
                message: error.message,
                timestamp: new Date().toISOString(),
                context: 'test'
            });
            
            assert.strictEqual(driveScanner.scanState.errors.length, 1);
            assert.strictEqual(driveScanner.scanState.errors[0].message, 'Test error');
        });
    });

    describe('Performance và Rate Limiting', () => {
        test('should respect batch size configuration', async () => {
            const folder = { id: 'test-folder', path: '/test', depth: 1 };
            
            await driveScanner.scanFolder(testUserEmail, folder, {});
            
            const apiHistory = googleDriveMock.getCallHistory();
            const listCall = apiHistory.find(call => call.method === 'listFiles');
            
            assert.strictEqual(listCall.options.pageSize, driveScanner.config.batchSize);
        });
        
        test('should handle large folder scanning', async () => {
            const folder = { id: 'large-folder', path: '/large', depth: 1 };
            
            // Add many mock files
            for (let i = 0; i < 150; i++) {
                const file = TestUtils.createMockFile(1024);
                file.parents = [folder.id];
                googleDriveMock.addMockFile(file);
            }
            
            const files = await driveScanner.scanFolder(testUserEmail, folder, {});
            
            // Should handle pagination
            assert(Array.isArray(files));
            
            // Verify multiple API calls were made for pagination
            const apiHistory = googleDriveMock.getCallHistory();
            const listCalls = apiHistory.filter(call => call.method === 'listFiles');
            assert(listCalls.length >= 1);
        });
    });
});
