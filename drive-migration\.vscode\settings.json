{"editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.exclude": {"**/node_modules": true, "**/temp": false, "**/backups": false, "**/checkpoints": false, "**/reports": false}, "search.exclude": {"**/node_modules": true, "**/temp": true, "**/backups": true, "**/checkpoints": true}, "javascript.preferences.includePackageJsonAutoImports": "auto", "typescript.preferences.includePackageJsonAutoImports": "auto", "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "terminal.integrated.fontSize": 14, "files.associations": {"*.env.example": "properties", "*.env": "properties"}, "emmet.includeLanguages": {"javascript": "javascriptreact"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.js": "${capture}.test.js,${capture}.spec.js", "package.json": "pnpm-lock.yaml,package-lock.json,yarn.lock"}, "git.ignoreLimitWarning": true, "workbench.colorCustomizations": {"activityBar.background": "#1e3a8a", "activityBar.foreground": "#ffffff"}, "files.watcherExclude": {"**/node_modules/**": true, "**/temp/**": true, "**/backups/**": true, "**/checkpoints/**": true}}