import React, { useState, useEffect } from 'react';
import FolderBrowser from './FolderBrowser';

const ScopeSelector = ({ onScopeSelected, userEmail }) => {
  const [selectedScope, setSelectedScope] = useState('all');
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [maxDepth, setMaxDepth] = useState(10);
  const [includeSharedDrives, setIncludeSharedDrives] = useState(true);
  const [filterOptions, setFilterOptions] = useState({
    includeGoogleDocs: true,
    includeImages: true,
    includePDFs: true,
    includeOfficeFiles: true,
    includeOthers: false
  });

  const handleScopeChange = (scope) => {
    setSelectedScope(scope);
    if (scope === 'all') {
      setSelectedFolder(null);
    }
  };

  const handleFolderSelected = (folder) => {
    setSelectedFolder(folder);
    setSelectedScope('folder');
  };

  const handleFilterChange = (filterType, value) => {
    setFilterOptions(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const getMimeTypesFromFilters = () => {
    const mimeTypes = [];
    
    if (filterOptions.includeGoogleDocs) {
      mimeTypes.push(
        'application/vnd.google-apps.document',
        'application/vnd.google-apps.spreadsheet',
        'application/vnd.google-apps.presentation'
      );
    }
    
    if (filterOptions.includeImages) {
      mimeTypes.push('image/jpeg', 'image/png', 'image/gif', 'image/bmp');
    }
    
    if (filterOptions.includePDFs) {
      mimeTypes.push('application/pdf');
    }
    
    if (filterOptions.includeOfficeFiles) {
      mimeTypes.push(
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      );
    }

    // Always include folders
    mimeTypes.push('application/vnd.google-apps.folder');
    
    return mimeTypes;
  };

  const handleStartScan = () => {
    if (!userEmail) {
      alert('Please enter your email address');
      return;
    }

    const options = {
      maxDepth,
      includeSharedDrives,
      filterMimeTypes: getMimeTypesFromFilters(),
      folderId: selectedScope === 'folder' ? selectedFolder?.id : null,
      folderPath: selectedScope === 'folder' ? selectedFolder?.path : null
    };

    onScopeSelected(selectedScope, options);
  };

  const isValidSelection = () => {
    if (!userEmail) return false;
    if (selectedScope === 'folder' && !selectedFolder) return false;
    return Object.values(filterOptions).some(value => value);
  };

  return (
    <div className="scope-selector">
      <h2>📁 Select Migration Scope</h2>
      
      <div className="scope-options">
        <div className="scope-option">
          <label className="radio-label">
            <input
              type="radio"
              name="scope"
              value="all"
              checked={selectedScope === 'all'}
              onChange={(e) => handleScopeChange(e.target.value)}
            />
            <span className="radio-custom"></span>
            <div className="option-content">
              <h3>🌐 Entire Drive</h3>
              <p>Scan and migrate all files from your Google Drive</p>
            </div>
          </label>
        </div>

        <div className="scope-option">
          <label className="radio-label">
            <input
              type="radio"
              name="scope"
              value="folder"
              checked={selectedScope === 'folder'}
              onChange={(e) => handleScopeChange(e.target.value)}
            />
            <span className="radio-custom"></span>
            <div className="option-content">
              <h3>📂 Specific Folder</h3>
              <p>Choose a specific folder to migrate</p>
            </div>
          </label>
        </div>
      </div>

      {selectedScope === 'folder' && (
        <div className="folder-selection">
          <h3>Choose Folder</h3>
          {selectedFolder ? (
            <div className="selected-folder">
              <span className="folder-icon">📁</span>
              <span className="folder-path">{selectedFolder.path || selectedFolder.name}</span>
              <button 
                onClick={() => setSelectedFolder(null)}
                className="btn btn-secondary btn-small"
              >
                Change
              </button>
            </div>
          ) : (
            <FolderBrowser 
              userEmail={userEmail}
              onFolderSelected={handleFolderSelected}
            />
          )}
        </div>
      )}

      <div className="scan-options">
        <h3>⚙️ Scan Options</h3>
        
        <div className="option-group">
          <label className="option-label">
            Maximum Folder Depth:
            <input
              type="number"
              min="1"
              max="100"
              value={maxDepth}
              onChange={(e) => setMaxDepth(parseInt(e.target.value))}
              className="number-input"
            />
          </label>
          <small>Limit how deep to scan nested folders (1-100)</small>
        </div>

        <div className="option-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={includeSharedDrives}
              onChange={(e) => setIncludeSharedDrives(e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            Include Shared Drives
          </label>
          <small>Scan files from shared/team drives</small>
        </div>
      </div>

      <div className="file-filters">
        <h3>📄 File Type Filters</h3>
        
        <div className="filter-grid">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={filterOptions.includeGoogleDocs}
              onChange={(e) => handleFilterChange('includeGoogleDocs', e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            Google Docs, Sheets, Slides
          </label>

          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={filterOptions.includeImages}
              onChange={(e) => handleFilterChange('includeImages', e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            Images (JPG, PNG, GIF)
          </label>

          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={filterOptions.includePDFs}
              onChange={(e) => handleFilterChange('includePDFs', e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            PDF Documents
          </label>

          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={filterOptions.includeOfficeFiles}
              onChange={(e) => handleFilterChange('includeOfficeFiles', e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            Office Files (Word, Excel, PowerPoint)
          </label>

          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={filterOptions.includeOthers}
              onChange={(e) => handleFilterChange('includeOthers', e.target.checked)}
            />
            <span className="checkbox-custom"></span>
            Other File Types
          </label>
        </div>
      </div>

      <div className="action-buttons">
        <button
          onClick={handleStartScan}
          disabled={!isValidSelection()}
          className="btn btn-primary btn-large"
        >
          🔍 Start Scanning
        </button>
      </div>

      {!isValidSelection() && (
        <div className="validation-message">
          {!userEmail && <p>⚠️ Please enter your email address</p>}
          {selectedScope === 'folder' && !selectedFolder && <p>⚠️ Please select a folder</p>}
          {!Object.values(filterOptions).some(v => v) && <p>⚠️ Please select at least one file type</p>}
        </div>
      )}
    </div>
  );
};

export default ScopeSelector;
