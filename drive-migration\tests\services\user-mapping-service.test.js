/**
 * Unit Tests cho User Mapping Service
 * Test tất cả functionality của user-mapping-service.js
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { UserMappingService } from '../../src/services/user-mapping-service.js';
import { TestUtils, TestAssertions, mockData } from '../test-config.js';
import { databaseMock } from '../mocks/database-mock.js';

describe('UserMappingService Tests', () => {
    let userMappingService;
    
    beforeEach(() => {
        // Reset mocks
        databaseMock.reset();
        
        // Create user mapping service instance với mocked dependencies
        userMappingService = new UserMappingService();
        
        // Mock supabaseClient
        global.supabaseClient = databaseMock;
    });
    
    afterEach(() => {
        databaseMock.clearCallHistory();
    });

    describe('Constructor và Configuration', () => {
        test('should initialize với default configuration', () => {
            const service = new UserMappingService();
            
            assert(Array.isArray(service.config.autoMappingRules));
            assert.strictEqual(service.config.similarityThreshold, 0.8);
            assert.strictEqual(service.config.batchSize, 100);
            
            // Verify auto mapping rules
            const ruleTypes = service.config.autoMappingRules.map(rule => rule.type);
            assert(ruleTypes.includes('same_domain'));
            assert(ruleTypes.includes('email_prefix'));
            assert(ruleTypes.includes('name_similarity'));
        });
        
        test('should initialize stats correctly', () => {
            const service = new UserMappingService();
            
            assert.strictEqual(service.stats.totalUsers, 0);
            assert.strictEqual(service.stats.mappedUsers, 0);
            assert.strictEqual(service.stats.unmappedUsers, 0);
            assert.strictEqual(service.stats.autoMappedUsers, 0);
            assert.strictEqual(service.stats.manualMappedUsers, 0);
            assert.strictEqual(service.stats.conflictUsers, 0);
        });
        
        test('should initialize cache correctly', () => {
            const service = new UserMappingService();
            
            assert(service.larkUsersCache instanceof Map);
            assert.strictEqual(service.larkUsersCache.size, 0);
            assert.strictEqual(service.cacheExpiry, null);
        });
    });

    describe('extractUniqueUsers Method', () => {
        test('should extract unique users from permissions', () => {
            const permissions = [
                {
                    emailAddress: '<EMAIL>',
                    displayName: 'User One',
                    role: 'reader'
                },
                {
                    emailAddress: '<EMAIL>',
                    displayName: 'User Two',
                    role: 'writer'
                },
                {
                    emailAddress: '<EMAIL>', // Duplicate
                    displayName: 'User One',
                    role: 'owner'
                }
            ];
            
            const uniqueUsers = userMappingService.extractUniqueUsers(permissions);
            
            assert.strictEqual(uniqueUsers.length, 2);
            assert(uniqueUsers.some(user => user.email === '<EMAIL>'));
            assert(uniqueUsers.some(user => user.email === '<EMAIL>'));
        });
        
        test('should handle permissions without email', () => {
            const permissions = [
                {
                    emailAddress: '<EMAIL>',
                    displayName: 'User One'
                },
                {
                    displayName: 'User Without Email'
                    // No emailAddress
                },
                {
                    emailAddress: null,
                    displayName: 'User With Null Email'
                }
            ];
            
            const uniqueUsers = userMappingService.extractUniqueUsers(permissions);
            
            assert.strictEqual(uniqueUsers.length, 1);
            assert.strictEqual(uniqueUsers[0].email, '<EMAIL>');
        });
        
        test('should handle empty permissions array', () => {
            const uniqueUsers = userMappingService.extractUniqueUsers([]);
            
            assert.strictEqual(uniqueUsers.length, 0);
        });
    });

    describe('findBestLarkMatch Method', () => {
        test('should find exact email match', () => {
            const googleUser = {
                email: '<EMAIL>',
                displayName: 'John Doe'
            };
            
            const larkUsers = [
                {
                    user_id: 'lark-user-1',
                    email: '<EMAIL>',
                    name: 'Jane Smith'
                },
                {
                    user_id: 'lark-user-2',
                    email: '<EMAIL>',
                    name: 'John Doe'
                }
            ];
            
            const match = userMappingService.findBestLarkMatch(googleUser, larkUsers);
            
            assert(match);
            assert.strictEqual(match.larkUserId, 'lark-user-2');
            assert.strictEqual(match.method, 'exact_email');
            assert.strictEqual(match.confidence, 1.0);
        });
        
        test('should find same domain prefix match', () => {
            const googleUser = {
                email: '<EMAIL>',
                displayName: 'John Doe'
            };
            
            const larkUsers = [
                {
                    user_id: 'lark-user-1',
                    email: '<EMAIL>', // Same domain and prefix
                    name: 'John D'
                }
            ];
            
            const match = userMappingService.findBestLarkMatch(googleUser, larkUsers);
            
            assert(match);
            assert.strictEqual(match.larkUserId, 'lark-user-1');
            assert.strictEqual(match.method, 'exact_email'); // Should prefer exact match
            assert.strictEqual(match.confidence, 1.0);
        });
        
        test('should find name similarity match', () => {
            const googleUser = {
                email: '<EMAIL>',
                displayName: 'John Doe'
            };
            
            const larkUsers = [
                {
                    user_id: 'lark-user-1',
                    email: '<EMAIL>',
                    name: 'John Doe' // Exact name match
                }
            ];
            
            const match = userMappingService.findBestLarkMatch(googleUser, larkUsers);
            
            assert(match);
            assert.strictEqual(match.larkUserId, 'lark-user-1');
            // Should find some match based on name similarity
            assert(match.confidence > 0.7);
        });
        
        test('should return null for no good matches', () => {
            const googleUser = {
                email: '<EMAIL>',
                displayName: 'John Doe'
            };
            
            const larkUsers = [
                {
                    user_id: 'lark-user-1',
                    email: '<EMAIL>',
                    name: 'Completely Different Person'
                }
            ];
            
            const match = userMappingService.findBestLarkMatch(googleUser, larkUsers);
            
            // Should return null or low confidence match
            assert(!match || match.confidence < userMappingService.config.similarityThreshold);
        });
    });

    describe('calculateStringSimilarity Method', () => {
        test('should calculate similarity correctly', () => {
            const testCases = [
                { str1: 'john doe', str2: 'john doe', expected: 1.0 },
                { str1: 'john doe', str2: 'john d', expected: 0.5 },
                { str1: 'john', str2: 'jane', expected: 0.5 },
                { str1: 'completely', str2: 'different', expected: 0.0 },
                { str1: '', str2: '', expected: 1.0 }
            ];
            
            testCases.forEach(({ str1, str2, expected }) => {
                const similarity = userMappingService.calculateStringSimilarity(str1, str2);
                assert(Math.abs(similarity - expected) < 0.1, 
                    `Expected ${expected} for "${str1}" vs "${str2}", got ${similarity}`);
            });
        });
        
        test('should handle edge cases', () => {
            assert.strictEqual(userMappingService.calculateStringSimilarity('', 'test'), 0);
            assert.strictEqual(userMappingService.calculateStringSimilarity('test', ''), 0);
            assert.strictEqual(userMappingService.calculateStringSimilarity('', ''), 1.0);
        });
    });

    describe('Database Operations', () => {
        test('should get existing mappings', async () => {
            // Add mock data
            const mockUsers = [
                {
                    id: TestUtils.generateUUID(),
                    email_google: '<EMAIL>',
                    lark_userid: 'lark-1',
                    mapped: true
                },
                {
                    id: TestUtils.generateUUID(),
                    email_google: '<EMAIL>',
                    lark_userid: null,
                    mapped: false
                }
            ];
            
            mockUsers.forEach(user => {
                databaseMock.addMockRecord('users', user);
            });
            
            const mappings = await userMappingService.getExistingMappings();
            
            assert(Array.isArray(mappings));
            assert(mappings.length >= 2);
            
            // Verify database was queried
            const dbHistory = databaseMock.getCallHistory();
            const selectCalls = dbHistory.filter(call => call.method === 'select');
            assert(selectCalls.length > 0);
        });
        
        test('should insert new users', async () => {
            const newUsers = [
                {
                    email: '<EMAIL>',
                    displayName: 'New User 1'
                },
                {
                    email: '<EMAIL>',
                    displayName: 'New User 2'
                }
            ];
            
            const result = await userMappingService.insertNewUsers(newUsers);
            
            assert.strictEqual(result.insertedCount, 2);
            assert(Array.isArray(result.insertedUsers));
            
            // Verify database was called
            const dbHistory = databaseMock.getCallHistory();
            const insertCalls = dbHistory.filter(call => call.method === 'insert');
            assert(insertCalls.length > 0);
        });
        
        test('should update user mapping', async () => {
            const googleEmail = '<EMAIL>';
            const larkUserId = 'lark-user-123';
            const notes = 'Test mapping';
            
            // Add existing user
            databaseMock.addMockRecord('users', {
                id: TestUtils.generateUUID(),
                email_google: googleEmail,
                lark_userid: null,
                mapped: false
            });
            
            const result = await userMappingService.updateUserMapping(
                googleEmail,
                larkUserId,
                true,
                notes
            );
            
            assert.strictEqual(result.success, true);
            assert(result.updatedUser);
            
            // Verify database was updated
            const dbHistory = databaseMock.getCallHistory();
            const updateCalls = dbHistory.filter(call => call.method === 'update');
            assert(updateCalls.length > 0);
        });
    });

    describe('initializeUserMapping Method', () => {
        test('should initialize user mapping successfully', async () => {
            const drivePermissions = [
                {
                    emailAddress: '<EMAIL>',
                    displayName: 'User One',
                    role: 'reader'
                },
                {
                    emailAddress: '<EMAIL>',
                    displayName: 'User Two',
                    role: 'writer'
                }
            ];
            
            const result = await userMappingService.initializeUserMapping(drivePermissions);
            
            assert.strictEqual(result.success, true);
            assert(typeof result.totalUsers === 'number');
            assert(typeof result.newUsers === 'number');
            assert(typeof result.existingUsers === 'number');
            assert(typeof result.autoMappedCount === 'number');
            assert(typeof result.unmappedCount === 'number');
        });
        
        test('should handle empty permissions', async () => {
            const result = await userMappingService.initializeUserMapping([]);
            
            assert.strictEqual(result.success, true);
            assert.strictEqual(result.totalUsers, 0);
            assert.strictEqual(result.newUsers, 0);
        });
    });

    describe('performAutoMapping Method', () => {
        test('should perform auto mapping successfully', async () => {
            const users = [
                {
                    email: '<EMAIL>',
                    displayName: 'John Doe'
                }
            ];
            
            // Mock Lark users
            userMappingService.larkUsersCache.set('lark-1', {
                user_id: 'lark-1',
                email: '<EMAIL>',
                name: 'John Doe'
            });
            
            const result = await userMappingService.performAutoMapping(users);
            
            assert(typeof result.autoMappedCount === 'number');
            assert(Array.isArray(result.mappingResults));
        });
        
        test('should handle users with no matches', async () => {
            const users = [
                {
                    email: '<EMAIL>',
                    displayName: 'No Match'
                }
            ];
            
            // Empty Lark users cache
            userMappingService.larkUsersCache.clear();
            
            const result = await userMappingService.performAutoMapping(users);
            
            assert.strictEqual(result.autoMappedCount, 0);
            assert.strictEqual(result.mappingResults.length, 0);
        });
    });

    describe('Statistics Methods', () => {
        test('should update statistics correctly', async () => {
            // Add mock users
            const mockUsers = [
                { id: '1', mapped: true },
                { id: '2', mapped: true },
                { id: '3', mapped: false }
            ];
            
            mockUsers.forEach(user => {
                databaseMock.addMockRecord('users', user);
            });
            
            const stats = await userMappingService.updateStatistics();
            
            assert.strictEqual(stats.totalUsers, 3);
            assert.strictEqual(stats.mappedUsers, 2);
            assert.strictEqual(stats.unmappedUsers, 1);
            assert.strictEqual(stats.mappingRate, (2/3) * 100);
        });
        
        test('should get current stats', () => {
            userMappingService.stats = {
                totalUsers: 10,
                mappedUsers: 7,
                unmappedUsers: 3,
                mappingRate: 70
            };
            
            const stats = userMappingService.getStats();
            
            assert.strictEqual(stats.totalUsers, 10);
            assert.strictEqual(stats.mappedUsers, 7);
            assert.strictEqual(stats.unmappedUsers, 3);
            assert.strictEqual(stats.mappingRate, 70);
        });
    });

    describe('Cache Management', () => {
        test('should cache Lark users', () => {
            const larkUsers = [
                {
                    user_id: 'lark-1',
                    email: '<EMAIL>',
                    name: 'User One'
                },
                {
                    user_id: 'lark-2',
                    email: '<EMAIL>',
                    name: 'User Two'
                }
            ];
            
            // Simulate caching
            userMappingService.larkUsersCache.clear();
            larkUsers.forEach(user => {
                userMappingService.larkUsersCache.set(user.user_id, user);
            });
            userMappingService.cacheExpiry = Date.now() + (60 * 60 * 1000);
            
            assert.strictEqual(userMappingService.larkUsersCache.size, 2);
            assert(userMappingService.larkUsersCache.has('lark-1'));
            assert(userMappingService.larkUsersCache.has('lark-2'));
            assert(userMappingService.cacheExpiry > Date.now());
        });
        
        test('should check cache expiry', () => {
            // Set expired cache
            userMappingService.cacheExpiry = Date.now() - 1000;
            
            const isExpired = userMappingService.cacheExpiry < Date.now();
            assert.strictEqual(isExpired, true);
            
            // Set valid cache
            userMappingService.cacheExpiry = Date.now() + (60 * 60 * 1000);
            
            const isValid = userMappingService.cacheExpiry > Date.now();
            assert.strictEqual(isValid, true);
        });
    });

    describe('Error Handling', () => {
        test('should handle database errors gracefully', async () => {
            // Simulate database error
            const restoreError = databaseMock.simulateError(
                'select',
                new Error('Database connection failed')
            );
            
            try {
                await assert.rejects(
                    userMappingService.getExistingMappings(),
                    /Database connection failed/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should handle insert errors', async () => {
            const newUsers = [
                { email: '<EMAIL>', displayName: 'Test User' }
            ];
            
            const restoreError = databaseMock.simulateError(
                'insert',
                new Error('Insert failed')
            );
            
            try {
                await assert.rejects(
                    userMappingService.insertNewUsers(newUsers),
                    /Insert failed/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should handle update errors', async () => {
            const restoreError = databaseMock.simulateError(
                'update',
                new Error('Update failed')
            );
            
            try {
                await assert.rejects(
                    userMappingService.updateUserMapping('<EMAIL>', 'lark-1'),
                    /Update failed/
                );
            } finally {
                restoreError();
            }
        });
    });

    describe('Validation và Edge Cases', () => {
        test('should handle malformed permissions', () => {
            const malformedPermissions = [
                null,
                undefined,
                {},
                { emailAddress: '' },
                { emailAddress: '   ' },
                { displayName: 'No Email' }
            ];
            
            const uniqueUsers = userMappingService.extractUniqueUsers(malformedPermissions);
            
            // Should filter out invalid entries
            assert.strictEqual(uniqueUsers.length, 0);
        });
        
        test('should handle empty Lark users list', () => {
            const googleUser = {
                email: '<EMAIL>',
                displayName: 'Test User'
            };
            
            const match = userMappingService.findBestLarkMatch(googleUser, []);
            
            assert.strictEqual(match, null);
        });
        
        test('should handle missing user properties', () => {
            const googleUser = {
                email: '<EMAIL>'
                // Missing displayName
            };
            
            const larkUsers = [
                {
                    user_id: 'lark-1'
                    // Missing email and name
                }
            ];
            
            const match = userMappingService.findBestLarkMatch(googleUser, larkUsers);
            
            // Should handle gracefully
            assert(!match || match.confidence < userMappingService.config.similarityThreshold);
        });
    });
});
