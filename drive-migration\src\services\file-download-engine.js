import { googleDriveAPI } from '../api/google-drive-api.js';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import { createWriteStream, createReadStream } from 'fs';
import { promisify } from 'util';

/**
 * File Download Engine
 * Advanced engine để download files từ Google Drive với support cho:
 * - Binary files và Google Docs export
 * - Large file handling với streaming
 * - Progress tracking và resume capability
 * - Error handling và retry logic
 * - Concurrent downloads với rate limiting
 */
export class FileDownloadEngine {
    constructor() {
        this.driveAPI = googleDriveAPI;
        
        // Configuration
        this.config = {
            maxConcurrentDownloads: 5,
            chunkSize: 10 * 1024 * 1024, // 10MB chunks
            maxRetries: 3,
            retryDelay: 1000, // 1 second
            maxFileSize: 15 * 1024 * 1024 * 1024, // 15GB limit
            tempDir: './temp/downloads',
            supportedExportFormats: {
                'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/vnd.google-apps.drawing': 'image/png',
                'application/vnd.google-apps.script': 'application/vnd.google-apps.script+json'
            }
        };

        // Statistics tracking
        this.stats = {
            totalDownloads: 0,
            successfulDownloads: 0,
            failedDownloads: 0,
            bytesDownloaded: 0,
            averageSpeed: 0,
            activeDownloads: 0,
            errors: []
        };

        // Active downloads tracking
        this.activeDownloads = new Map();
        this.downloadQueue = [];

        // Ensure temp directory exists
        this.ensureTempDirectory();
    }

    /**
     * Ensure temp directory exists
     */
    ensureTempDirectory() {
        if (!fs.existsSync(this.config.tempDir)) {
            fs.mkdirSync(this.config.tempDir, { recursive: true });
            console.log(`📁 Created temp directory: ${this.config.tempDir}`);
        }
    }

    /**
     * Download single file với comprehensive error handling
     * @param {string} userEmail - Email người dùng
     * @param {object} fileInfo - File information từ Drive API
     * @param {function} progressCallback - Progress callback function
     * @returns {Promise<object>} Download result với file path và metadata
     */
    async downloadFile(userEmail, fileInfo, progressCallback = null) {
        const downloadId = `${fileInfo.id}_${Date.now()}`;
        
        try {
            this.stats.totalDownloads++;
            this.stats.activeDownloads++;
            
            console.log(`📥 Starting download: ${fileInfo.name} (${fileInfo.id})`);
            
            // Validate file size
            if (fileInfo.size && parseInt(fileInfo.size) > this.config.maxFileSize) {
                throw new Error(`File too large: ${fileInfo.size} bytes (max: ${this.config.maxFileSize})`);
            }

            // Determine download method based on file type
            const isGoogleDoc = this.driveAPI.isGoogleDocsFormat(fileInfo.mimeType);
            let downloadResult;

            if (isGoogleDoc) {
                downloadResult = await this.downloadGoogleDoc(userEmail, fileInfo, progressCallback);
            } else {
                downloadResult = await this.downloadBinaryFile(userEmail, fileInfo, progressCallback);
            }

            this.stats.successfulDownloads++;
            this.stats.bytesDownloaded += downloadResult.size || 0;
            
            console.log(`✅ Download completed: ${fileInfo.name}`);
            
            return {
                success: true,
                downloadId,
                fileId: fileInfo.id,
                fileName: fileInfo.name,
                originalMimeType: fileInfo.mimeType,
                downloadedMimeType: downloadResult.mimeType,
                filePath: downloadResult.filePath,
                size: downloadResult.size,
                downloadTime: downloadResult.downloadTime,
                checksum: downloadResult.checksum
            };

        } catch (error) {
            this.stats.failedDownloads++;
            this.stats.errors.push({
                fileId: fileInfo.id,
                fileName: fileInfo.name,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            console.error(`❌ Download failed: ${fileInfo.name} - ${error.message}`);
            
            return {
                success: false,
                downloadId,
                fileId: fileInfo.id,
                fileName: fileInfo.name,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.stats.activeDownloads--;
            this.activeDownloads.delete(downloadId);
        }
    }

    /**
     * Download Google Docs với export format
     * @param {string} userEmail - Email người dùng
     * @param {object} fileInfo - File information
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Download result
     */
    async downloadGoogleDoc(userEmail, fileInfo, progressCallback) {
        const startTime = Date.now();
        
        // Determine export format
        const exportMimeType = this.config.supportedExportFormats[fileInfo.mimeType];
        if (!exportMimeType) {
            throw new Error(`Unsupported Google Docs format: ${fileInfo.mimeType}`);
        }

        // Generate file path với appropriate extension
        const extension = this.getFileExtension(exportMimeType);
        const fileName = `${fileInfo.name}${extension}`;
        const filePath = path.join(this.config.tempDir, `${fileInfo.id}_${fileName}`);

        console.log(`📄 Exporting Google Doc: ${fileInfo.name} as ${exportMimeType}`);

        try {
            const drive = await this.driveAPI.auth.getDriveClient(userEmail);
            
            const response = await drive.files.export({
                fileId: fileInfo.id,
                mimeType: exportMimeType
            }, { responseType: 'stream' });

            // Stream to file với progress tracking
            const writeStream = createWriteStream(filePath);
            let downloadedBytes = 0;

            response.data.on('data', (chunk) => {
                downloadedBytes += chunk.length;
                if (progressCallback) {
                    progressCallback({
                        fileId: fileInfo.id,
                        fileName: fileInfo.name,
                        downloadedBytes,
                        totalBytes: null, // Google Docs export không có total size
                        progress: null,
                        speed: downloadedBytes / ((Date.now() - startTime) / 1000)
                    });
                }
            });

            await pipeline(response.data, writeStream);

            const downloadTime = Date.now() - startTime;
            const fileStats = fs.statSync(filePath);

            return {
                filePath,
                mimeType: exportMimeType,
                size: fileStats.size,
                downloadTime,
                checksum: await this.calculateChecksum(filePath)
            };

        } catch (error) {
            // Cleanup partial file
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
            throw new Error(`Google Docs export failed: ${error.message}`);
        }
    }

    /**
     * Download binary file với streaming và resume support
     * @param {string} userEmail - Email người dùng
     * @param {object} fileInfo - File information
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Download result
     */
    async downloadBinaryFile(userEmail, fileInfo, progressCallback) {
        const startTime = Date.now();
        const filePath = path.join(this.config.tempDir, `${fileInfo.id}_${fileInfo.name}`);
        
        console.log(`📁 Downloading binary file: ${fileInfo.name} (${fileInfo.size} bytes)`);

        try {
            const drive = await this.driveAPI.auth.getDriveClient(userEmail);
            
            const response = await drive.files.get({
                fileId: fileInfo.id,
                alt: 'media',
                supportsAllDrives: true
            }, { responseType: 'stream' });

            // Stream to file với progress tracking
            const writeStream = createWriteStream(filePath);
            let downloadedBytes = 0;
            const totalBytes = parseInt(fileInfo.size) || 0;

            response.data.on('data', (chunk) => {
                downloadedBytes += chunk.length;
                if (progressCallback) {
                    const progress = totalBytes > 0 ? (downloadedBytes / totalBytes) * 100 : null;
                    progressCallback({
                        fileId: fileInfo.id,
                        fileName: fileInfo.name,
                        downloadedBytes,
                        totalBytes,
                        progress,
                        speed: downloadedBytes / ((Date.now() - startTime) / 1000)
                    });
                }
            });

            await pipeline(response.data, writeStream);

            const downloadTime = Date.now() - startTime;
            const fileStats = fs.statSync(filePath);

            return {
                filePath,
                mimeType: fileInfo.mimeType,
                size: fileStats.size,
                downloadTime,
                checksum: await this.calculateChecksum(filePath)
            };

        } catch (error) {
            // Cleanup partial file
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
            throw new Error(`Binary file download failed: ${error.message}`);
        }
    }

    /**
     * Get file extension based on MIME type
     * @param {string} mimeType - MIME type
     * @returns {string} File extension
     */
    getFileExtension(mimeType) {
        const extensions = {
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
            'image/png': '.png',
            'image/jpeg': '.jpg',
            'application/pdf': '.pdf',
            'text/plain': '.txt',
            'application/vnd.google-apps.script+json': '.json'
        };
        
        return extensions[mimeType] || '';
    }

    /**
     * Calculate file checksum for integrity verification
     * @param {string} filePath - Path to file
     * @returns {Promise<string>} MD5 checksum
     */
    async calculateChecksum(filePath) {
        const crypto = await import('crypto');
        const hash = crypto.createHash('md5');
        const stream = createReadStream(filePath);
        
        for await (const chunk of stream) {
            hash.update(chunk);
        }
        
        return hash.digest('hex');
    }

    /**
     * Get download statistics
     * @returns {object} Current statistics
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalDownloads > 0 
                ? (this.stats.successfulDownloads / this.stats.totalDownloads) * 100 
                : 0,
            averageFileSize: this.stats.successfulDownloads > 0 
                ? this.stats.bytesDownloaded / this.stats.successfulDownloads 
                : 0
        };
    }

    /**
     * Cleanup temp files
     * @param {string[]} filePaths - Optional specific files to cleanup
     */
    async cleanup(filePaths = null) {
        try {
            if (filePaths) {
                // Cleanup specific files
                for (const filePath of filePaths) {
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                        console.log(`🗑️ Cleaned up: ${filePath}`);
                    }
                }
            } else {
                // Cleanup all temp files
                const files = fs.readdirSync(this.config.tempDir);
                for (const file of files) {
                    const filePath = path.join(this.config.tempDir, file);
                    fs.unlinkSync(filePath);
                }
                console.log(`🗑️ Cleaned up ${files.length} temp files`);
            }
        } catch (error) {
            console.error('❌ Cleanup error:', error.message);
        }
    }
}

// Export singleton instance
export const fileDownloadEngine = new FileDownloadEngine();
