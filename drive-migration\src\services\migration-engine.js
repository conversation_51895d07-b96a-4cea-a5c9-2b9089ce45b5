import { fileDownloadEngine } from './file-download-engine.js';
import { larkUploadEngine } from './lark-upload-engine.js';
import { userMappingService } from './user-mapping-service.js';
import { realtimeService } from './realtime-service.js';
import { supabaseClient } from '../database/supabase.js';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { larkDriveAPI } from '../api/lark-drive-api.js';
import path from 'path';
import fs from 'fs';

/**
 * Migration Engine
 * Core orchestration engine cho toàn bộ quá trình migration từ Google Drive sang Lark Drive
 * Bao gồm:
 * - File download từ Google Drive
 * - File upload lên Lark Drive
 * - Permission mapping và assignment
 * - Progress tracking và error handling
 * - Resume capability và checkpoint system
 */
export class MigrationEngine {
    constructor() {
        this.downloadEngine = fileDownloadEngine;
        this.uploadEngine = larkUploadEngine;
        this.userMapping = userMappingService;
        this.realtime = realtimeService;
        this.googleAPI = googleDriveAPI;
        this.larkAPI = larkDriveAPI;

        // Configuration
        this.config = {
            maxConcurrentMigrations: 3,
            maxRetries: 3,
            retryDelay: 5000, // 5 seconds
            checkpointInterval: 10, // Save checkpoint every 10 files
            tempDir: './temp/migration',
            cleanupAfterSuccess: true,
            preserveFolderStructure: true
        };

        // Migration state tracking
        this.activeMigrations = new Map();
        this.migrationQueue = [];

        // Statistics
        this.stats = {
            totalMigrations: 0,
            successfulMigrations: 0,
            failedMigrations: 0,
            totalFiles: 0,
            processedFiles: 0,
            totalBytes: 0,
            processedBytes: 0,
            averageSpeed: 0,
            estimatedTimeRemaining: 0,
            errors: []
        };

        // Ensure temp directory exists
        this.ensureTempDirectory();
    }

    /**
     * Ensure temp directory exists
     */
    ensureTempDirectory() {
        if (!fs.existsSync(this.config.tempDir)) {
            fs.mkdirSync(this.config.tempDir, { recursive: true });
            console.log(`📁 Created migration temp directory: ${this.config.tempDir}`);
        }
    }

    /**
     * Start migration process cho selected files
     * @param {string} userEmail - Google user email
     * @param {string} sessionId - Scan session ID
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback function
     * @returns {Promise<object>} Migration result
     */
    async startMigration(userEmail, sessionId, options = {}, progressCallback = null) {
        const migrationId = `migration_${sessionId}_${Date.now()}`;

        try {
            console.log(`🚀 Starting migration: ${migrationId}`);

            // Initialize migration record
            const migration = await this.initializeMigration(migrationId, userEmail, sessionId, options);
            this.activeMigrations.set(migrationId, migration);

            // Create realtime channel for progress updates
            this.realtime.createMigrationChannel(migrationId);

            // Broadcast migration started
            await this.realtime.broadcastStatusChange(migrationId, 'running', {
                previousStatus: 'pending',
                reason: 'Migration started'
            });

            // Get selected files for migration
            const selectedFiles = await this.getSelectedFiles(sessionId);
            if (selectedFiles.length === 0) {
                throw new Error('No files selected for migration');
            }

            console.log(`📊 Migration scope: ${selectedFiles.length} files`);

            // Update migration with file count
            await this.updateMigrationProgress(migrationId, {
                total_files: selectedFiles.length,
                total_size: selectedFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0)
            });

            // Initialize user mapping if needed
            if (options.mapPermissions) {
                await this.initializeUserMappingForMigration(selectedFiles);
            }

            // Create folder structure mapping
            const folderMapping = await this.createFolderStructureMapping(selectedFiles, options.targetRootFolder);

            // Process files in batches
            const results = await this.processMigrationBatch(
                migrationId,
                userEmail,
                selectedFiles,
                folderMapping,
                options,
                progressCallback
            );

            // Finalize migration
            const finalResult = await this.finalizeMigration(migrationId, results);

            // Broadcast migration completion
            await this.realtime.broadcastMigrationComplete(migrationId, finalResult);

            console.log(`✅ Migration completed: ${migrationId}`);
            return finalResult;

        } catch (error) {
            console.error(`❌ Migration failed: ${migrationId} - ${error.message}`);

            // Broadcast error
            await this.realtime.broadcastError(migrationId, {
                severity: 'critical',
                errorMessage: error.message,
                retryable: false
            });

            await this.handleMigrationError(migrationId, error);
            throw error;
        } finally {
            this.activeMigrations.delete(migrationId);
        }
    }

    /**
     * Initialize migration record in database
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {string} sessionId - Session ID
     * @param {object} options - Migration options
     * @returns {Promise<object>} Migration record
     */
    async initializeMigration(migrationId, userEmail, sessionId, options) {
        try {
            const migrationData = {
                id: migrationId,
                user_email: userEmail,
                scan_session_id: sessionId,
                status: 'running',
                options: options,
                total_files: 0,
                processed_files: 0,
                successful_files: 0,
                failed_files: 0,
                total_size: 0,
                processed_size: 0,
                started_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const { data, error } = await supabaseClient
                .from('migration_tasks')
                .insert(migrationData)
                .select()
                .single();

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return data;
        } catch (error) {
            console.error('❌ Error initializing migration:', error.message);
            throw error;
        }
    }

    /**
     * Get selected files for migration
     * @param {string} sessionId - Scan session ID
     * @returns {Promise<Array>} Selected files
     */
    async getSelectedFiles(sessionId) {
        try {
            const { data, error } = await supabaseClient
                .from('scanned_files')
                .select('*')
                .eq('scan_session_id', sessionId)
                .eq('is_selected', true)
                .order('full_path', { ascending: true });

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return data || [];
        } catch (error) {
            console.error('❌ Error getting selected files:', error.message);
            throw error;
        }
    }

    /**
     * Initialize user mapping for migration
     * @param {Array} files - Files to migrate
     */
    async initializeUserMappingForMigration(files) {
        try {
            console.log('👥 Initializing user mapping for migration...');

            // Extract all permissions from files
            const allPermissions = [];
            for (const file of files) {
                if (file.permissions && Array.isArray(file.permissions)) {
                    allPermissions.push(...file.permissions);
                }
            }

            if (allPermissions.length > 0) {
                await this.userMapping.initializeUserMapping(allPermissions);
                console.log('✅ User mapping initialized for migration');
            }
        } catch (error) {
            console.warn('⚠️ User mapping initialization failed:', error.message);
            // Don't fail migration if user mapping fails
        }
    }

    /**
     * Create folder structure mapping
     * @param {Array} files - Files to migrate
     * @param {string} targetRootFolder - Target root folder in Lark
     * @returns {Promise<Map>} Folder mapping (Google path -> Lark folder ID)
     */
    async createFolderStructureMapping(files, targetRootFolder = null) {
        try {
            console.log('📁 Creating folder structure mapping...');

            const folderMapping = new Map();
            const uniqueFolders = new Set();

            // Extract unique folder paths
            for (const file of files) {
                const folderPath = path.dirname(file.full_path);
                if (folderPath && folderPath !== '.' && folderPath !== '/') {
                    uniqueFolders.add(folderPath);
                }
            }

            // Sort folders by depth (shallow first)
            const sortedFolders = Array.from(uniqueFolders).sort((a, b) => {
                return a.split('/').length - b.split('/').length;
            });

            // Create folders in Lark Drive
            for (const folderPath of sortedFolders) {
                try {
                    const larkFolder = await this.uploadEngine.createFolderStructure(
                        folderPath,
                        targetRootFolder
                    );

                    folderMapping.set(folderPath, larkFolder.token);
                    console.log(`✅ Mapped folder: ${folderPath} -> ${larkFolder.token}`);
                } catch (error) {
                    console.error(`❌ Failed to create folder ${folderPath}:`, error.message);
                    // Continue with other folders
                }
            }

            return folderMapping;
        } catch (error) {
            console.error('❌ Error creating folder structure mapping:', error.message);
            return new Map();
        }
    }

    /**
     * Process migration batch
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {Array} files - Files to migrate
     * @param {Map} folderMapping - Folder mapping
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<Array>} Migration results
     */
    async processMigrationBatch(migrationId, userEmail, files, folderMapping, options, progressCallback) {
        const results = [];
        let processedCount = 0;
        let successCount = 0;
        let failureCount = 0;

        console.log(`📦 Processing ${files.length} files in batches...`);

        // Process files in smaller batches to manage memory
        const batchSize = this.config.maxConcurrentMigrations;

        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);

            const batchPromises = batch.map(async (file) => {
                return await this.migrateFile(
                    migrationId,
                    userEmail,
                    file,
                    folderMapping,
                    options,
                    (fileProgress) => {
                        if (progressCallback) {
                            progressCallback({
                                type: 'file_progress',
                                migrationId,
                                fileId: file.file_id,
                                fileName: file.name,
                                ...fileProgress
                            });
                        }
                    }
                );
            });

            const batchResults = await Promise.allSettled(batchPromises);

            // Process batch results
            for (const result of batchResults) {
                processedCount++;

                if (result.status === 'fulfilled' && result.value.success) {
                    successCount++;
                    results.push(result.value);
                } else {
                    failureCount++;
                    const error = result.status === 'rejected' ? result.reason : result.value.error;
                    results.push({
                        success: false,
                        error: error?.message || 'Unknown error',
                        fileId: batch[batchResults.indexOf(result)]?.file_id
                    });
                }

                // Update progress
                const progressData = {
                    type: 'batch_progress',
                    migrationId,
                    processedFiles: processedCount,
                    totalFiles: files.length,
                    successfulFiles: successCount,
                    failedFiles: failureCount,
                    progress: (processedCount / files.length) * 100
                };

                if (progressCallback) {
                    progressCallback(progressData);
                }

                // Broadcast real-time progress
                await this.realtime.broadcastBatchProgress(migrationId, progressData);
            }

            // Update migration progress in database
            await this.updateMigrationProgress(migrationId, {
                processed_files: processedCount,
                successful_files: successCount,
                failed_files: failureCount
            });

            // Save checkpoint
            if (processedCount % this.config.checkpointInterval === 0) {
                await this.saveCheckpoint(migrationId, processedCount, results);
            }

            // Small delay between batches
            if (i + batchSize < files.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return results;
    }

    /**
     * Migrate single file
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {object} file - File to migrate
     * @param {Map} folderMapping - Folder mapping
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Migration result
     */
    async migrateFile(migrationId, userEmail, file, folderMapping, options, progressCallback) {
        try {
            console.log(`📄 Migrating file: ${file.name} (${file.file_id})`);

            // Step 1: Download file from Google Drive
            const downloadResult = await this.downloadEngine.downloadFile(
                userEmail,
                file,
                async (progress) => {
                    const fileProgressData = {
                        phase: 'downloading',
                        ...progress
                    };

                    if (progressCallback) {
                        progressCallback(fileProgressData);
                    }

                    // Broadcast real-time file progress
                    await this.realtime.broadcastFileProgress(migrationId, fileProgressData);
                }
            );

            if (!downloadResult.success) {
                throw new Error(`Download failed: ${downloadResult.error}`);
            }

            // Step 2: Determine target folder in Lark
            const targetFolderId = this.getTargetFolderId(file, folderMapping);

            // Step 3: Upload file to Lark Drive
            const uploadResult = await this.uploadEngine.uploadFile(
                downloadResult.filePath,
                file.name,
                targetFolderId,
                async (progress) => {
                    const fileProgressData = {
                        phase: 'uploading',
                        ...progress
                    };

                    if (progressCallback) {
                        progressCallback(fileProgressData);
                    }

                    // Broadcast real-time file progress
                    await this.realtime.broadcastFileProgress(migrationId, fileProgressData);
                }
            );

            if (!uploadResult.success) {
                throw new Error(`Upload failed: ${uploadResult.error}`);
            }

            // Step 4: Set permissions if enabled
            if (options.mapPermissions && file.permissions) {
                await this.setFilePermissions(uploadResult.fileToken, file.permissions);
            }

            // Step 5: Cleanup temp file if configured
            if (this.config.cleanupAfterSuccess) {
                await this.downloadEngine.cleanup([downloadResult.filePath]);
            }

            // Step 6: Record migration item
            await this.recordMigrationItem(migrationId, file, downloadResult, uploadResult);

            return {
                success: true,
                fileId: file.file_id,
                fileName: file.name,
                larkFileToken: uploadResult.fileToken,
                downloadResult,
                uploadResult
            };

        } catch (error) {
            console.error(`❌ File migration failed: ${file.name} - ${error.message}`);

            // Broadcast file error
            await this.realtime.broadcastError(migrationId, {
                severity: 'error',
                fileId: file.file_id,
                fileName: file.name,
                errorMessage: error.message,
                retryable: true
            });

            // Record failed migration
            await this.recordMigrationItem(migrationId, file, null, null, error.message);

            return {
                success: false,
                fileId: file.file_id,
                fileName: file.name,
                error: error.message
            };
        }
    }

    /**
     * Get target folder ID in Lark for a file
     * @param {object} file - File object
     * @param {Map} folderMapping - Folder mapping
     * @returns {string|null} Target folder ID
     */
    getTargetFolderId(file, folderMapping) {
        const folderPath = path.dirname(file.full_path);

        if (folderPath && folderPath !== '.' && folderPath !== '/') {
            return folderMapping.get(folderPath) || null;
        }

        return null; // Root folder
    }

    /**
     * Set file permissions in Lark Drive
     * @param {string} fileToken - Lark file token
     * @param {Array} googlePermissions - Google Drive permissions
     */
    async setFilePermissions(fileToken, googlePermissions) {
        try {
            // This is a placeholder - will implement detailed permission mapping
            console.log(`🔐 Setting permissions for file: ${fileToken}`);

            // For now, just set basic permissions
            await this.larkAPI.setPermissions(fileToken, {
                external_access: false,
                security_entity: 'tenant_editable'
            });

        } catch (error) {
            console.warn(`⚠️ Failed to set permissions for ${fileToken}:`, error.message);
            // Don't fail migration if permission setting fails
        }
    }

    /**
     * Update migration progress in database
     * @param {string} migrationId - Migration ID
     * @param {object} updates - Updates to apply
     */
    async updateMigrationProgress(migrationId, updates) {
        try {
            const { error } = await supabaseClient
                .from('migration_tasks')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', migrationId);

            if (error) {
                console.error('❌ Error updating migration progress:', error.message);
            }
        } catch (error) {
            console.error('❌ Error updating migration progress:', error.message);
        }
    }

    /**
     * Record migration item in database
     * @param {string} migrationId - Migration ID
     * @param {object} file - File object
     * @param {object} downloadResult - Download result
     * @param {object} uploadResult - Upload result
     * @param {string} errorMessage - Error message if failed
     */
    async recordMigrationItem(migrationId, file, downloadResult, uploadResult, errorMessage = null) {
        try {
            const itemData = {
                migration_task_id: migrationId,
                google_file_id: file.file_id,
                google_file_name: file.name,
                google_file_path: file.full_path,
                google_file_size: parseInt(file.size) || 0,
                lark_file_token: uploadResult?.fileToken || null,
                lark_file_name: uploadResult?.fileName || null,
                status: errorMessage ? 'failed' : 'completed',
                error_message: errorMessage,
                download_time: downloadResult?.downloadTime || null,
                upload_time: uploadResult?.uploadTime || null,
                created_at: new Date().toISOString()
            };

            const { error } = await supabaseClient
                .from('migration_items')
                .insert(itemData);

            if (error) {
                console.error('❌ Error recording migration item:', error.message);
            }
        } catch (error) {
            console.error('❌ Error recording migration item:', error.message);
        }
    }

    /**
     * Save migration checkpoint
     * @param {string} migrationId - Migration ID
     * @param {number} processedCount - Processed files count
     * @param {Array} results - Current results
     */
    async saveCheckpoint(migrationId, processedCount, results) {
        try {
            console.log(`💾 Saving checkpoint: ${migrationId} - ${processedCount} files processed`);

            const checkpointData = {
                processed_count: processedCount,
                timestamp: new Date().toISOString(),
                results_summary: {
                    total: results.length,
                    successful: results.filter(r => r.success).length,
                    failed: results.filter(r => !r.success).length
                }
            };

            await this.updateMigrationProgress(migrationId, {
                checkpoint_data: checkpointData
            });

        } catch (error) {
            console.error('❌ Error saving checkpoint:', error.message);
        }
    }

    /**
     * Finalize migration
     * @param {string} migrationId - Migration ID
     * @param {Array} results - Migration results
     * @returns {Promise<object>} Final result
     */
    async finalizeMigration(migrationId, results) {
        try {
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.filter(r => !r.success).length;

            const finalData = {
                status: failureCount === 0 ? 'completed' : 'completed_with_errors',
                successful_files: successCount,
                failed_files: failureCount,
                completed_at: new Date().toISOString()
            };

            await this.updateMigrationProgress(migrationId, finalData);

            return {
                migrationId,
                success: true,
                totalFiles: results.length,
                successfulFiles: successCount,
                failedFiles: failureCount,
                results
            };
        } catch (error) {
            console.error('❌ Error finalizing migration:', error.message);
            throw error;
        }
    }

    /**
     * Handle migration error
     * @param {string} migrationId - Migration ID
     * @param {Error} error - Error object
     */
    async handleMigrationError(migrationId, error) {
        try {
            await this.updateMigrationProgress(migrationId, {
                status: 'failed',
                error_message: error.message,
                completed_at: new Date().toISOString()
            });
        } catch (updateError) {
            console.error('❌ Error updating migration error status:', updateError.message);
        }
    }

    /**
     * Get migration statistics
     * @returns {object} Current statistics
     */
    getStats() {
        return this.stats;
    }

    /**
     * Get active migrations
     * @returns {Array} Active migration information
     */
    getActiveMigrations() {
        return Array.from(this.activeMigrations.entries()).map(([id, migration]) => ({
            migrationId: id,
            ...migration
        }));
    }
}

// Export singleton instance
export const migrationEngine = new MigrationEngine();
