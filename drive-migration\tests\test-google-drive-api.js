import { googleDriveAPI } from '../src/api/google-drive-api.js';

/**
 * Test Google Drive API operations
 */
async function testGoogleDriveAPI() {
    console.log('🔍 Testing Google Drive API Operations...\n');
    console.log('='.repeat(60));

    const testEmail = process.argv[2];

    if (!testEmail) {
        console.log('⚠️ No test email provided. Please provide as command line argument:');
        console.log('Usage: node test-google-drive-api.js <EMAIL>');
        return;
    }

    try {
        console.log(`👤 Testing with user: ${testEmail}\n`);

        // 1. Test comprehensive API operations
        console.log('1. Running comprehensive API tests...');
        const apiResults = await googleDriveAPI.testAPIs(testEmail);

        if (apiResults.success) {
            console.log('✅ All API operations successful!');
            console.log(`📊 Performance summary:`);
            Object.entries(apiResults.performance).forEach(([op, time]) => {
                console.log(`   ${op}: ${time}ms`);
            });
        } else {
            console.log('❌ Some API operations failed:');
            apiResults.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });
        }

        // 2. Test file listing with different options
        console.log('\n2. Testing file listing with various options...');

        // List recent files
        console.log('   📅 Recent files...');
        const recentFiles = await googleDriveAPI.listFiles(testEmail, {
            pageSize: 5,
            orderBy: 'modifiedTime desc',
            q: 'trashed=false'
        });
        console.log(`   ✅ Found ${recentFiles.files.length} recent files`);

        // List Google Docs only
        console.log('   📄 Google Docs only...');
        const googleDocs = await googleDriveAPI.listFiles(testEmail, {
            pageSize: 5,
            q: "trashed=false and (mimeType='application/vnd.google-apps.document' or mimeType='application/vnd.google-apps.spreadsheet')"
        });
        console.log(`   ✅ Found ${googleDocs.files.length} Google Docs`);

        // List large files
        console.log('   📦 Large files (>1MB)...');
        const largeFiles = await googleDriveAPI.listFiles(testEmail, {
            pageSize: 5,
            q: 'trashed=false and fileSize > 1048576'
        });
        console.log(`   ✅ Found ${largeFiles.files.length} large files`);

        // 3. Test file details and permissions
        if (recentFiles.files.length > 0) {
            console.log('\n3. Testing file details and permissions...');
            const testFile = recentFiles.files[0];

            console.log(`   📄 Testing with file: ${testFile.name}`);

            // Get detailed file info
            const fileDetails = await googleDriveAPI.getFile(testEmail, testFile.id);
            console.log(`   ✅ File details: ${fileDetails.name} (${fileDetails.sizeFormatted})`);
            console.log(`   📅 Modified: ${new Date(fileDetails.modifiedTime).toLocaleDateString()}`);
            console.log(`   🏷️ Type: ${fileDetails.mimeType}`);
            console.log(`   📁 Google Doc: ${fileDetails.isGoogleDoc ? 'Yes' : 'No'}`);

            // Get permissions
            const permissionsData = await googleDriveAPI.getFilePermissions(testEmail, testFile.id);
            console.log(`   🔐 Permissions: ${permissionsData.permissions.length} total`);
            console.log(`   📊 Analysis:`);
            console.log(`      Owners: ${permissionsData.analysis.owners}`);
            console.log(`      Editors: ${permissionsData.analysis.editors}`);
            console.log(`      Viewers: ${permissionsData.analysis.viewers}`);
            console.log(`      Public access: ${permissionsData.analysis.publicAccess ? 'Yes' : 'No'}`);
            console.log(`      External users: ${permissionsData.analysis.externalUsers}`);
        }

        // 4. Test batch operations
        if (recentFiles.files.length >= 3) {
            console.log('\n4. Testing batch operations...');

            const testFileIds = recentFiles.files.slice(0, 3).map(f => f.id);
            console.log(`   📁 Testing with ${testFileIds.length} files...`);

            // Batch file details
            const batchFiles = await googleDriveAPI.getMultipleFiles(testEmail, testFileIds, (progress) => {
                console.log(`   📊 Progress: ${progress.current}/${progress.total} files`);
            });

            console.log(`   ✅ Batch files: ${batchFiles.files.length} success, ${batchFiles.errors.length} errors`);

            // Batch permissions
            const batchPermissions = await googleDriveAPI.getMultipleFilePermissions(testEmail, testFileIds, (progress) => {
                console.log(`   🔐 Permissions progress: ${progress.current}/${progress.total} files`);
            });

            console.log(`   ✅ Batch permissions: ${Object.keys(batchPermissions.results).length} success, ${batchPermissions.errors.length} errors`);
            console.log(`   📊 Aggregate analysis:`);
            console.log(`      Files with public access: ${batchPermissions.analysis.filesWithPublicAccess}`);
            console.log(`      Files with external users: ${batchPermissions.analysis.filesWithExternalUsers}`);
        }

        // 5. Test pagination
        console.log('\n5. Testing pagination...');
        const allFiles = await googleDriveAPI.getAllFiles(testEmail, { pageSize: 10 }, (progress) => {
            console.log(`   📄 Page ${progress.page}: ${progress.filesFound} files total`);
        });
        console.log(`   ✅ Retrieved all ${allFiles.length} files via pagination`);

        // 6. Test caching performance
        console.log('\n6. Testing caching performance...');
        if (recentFiles.files.length > 0) {
            const testFileId = recentFiles.files[0].id;

            // Clear cache for fair test
            googleDriveAPI.clearCache();

            // Cold call
            const coldStart = Date.now();
            await googleDriveAPI.getFile(testEmail, testFileId);
            const coldTime = Date.now() - coldStart;

            // Warm calls
            const warmTimes = [];
            for (let i = 0; i < 3; i++) {
                const start = Date.now();
                await googleDriveAPI.getFile(testEmail, testFileId);
                warmTimes.push(Date.now() - start);
            }

            const avgWarmTime = warmTimes.reduce((a, b) => a + b, 0) / warmTimes.length;
            console.log(`   ❄️ Cold call: ${coldTime}ms`);
            console.log(`   🔥 Warm calls average: ${avgWarmTime.toFixed(2)}ms`);
            console.log(`   🚀 Cache speedup: ${(coldTime / avgWarmTime).toFixed(1)}x faster`);
        }

        // 7. Test file path building
        if (recentFiles.files.length > 0) {
            console.log('\n7. Testing file path building...');
            const testFile = recentFiles.files[0];

            if (testFile.parents && testFile.parents.length > 0) {
                const filePath = await googleDriveAPI.buildFilePath(testEmail, testFile.id, testFile.parents);
                console.log(`   📁 File path: ${filePath}/${testFile.name}`);
            } else {
                console.log(`   📁 File is in root directory: /${testFile.name}`);
            }
        }

        // 8. Show final statistics
        console.log('\n8. Final statistics...');
        const stats = googleDriveAPI.getStats();
        console.log(`   📊 API calls made: ${stats.apiCalls}`);
        console.log(`   ❌ Errors encountered: ${stats.errors}`);
        console.log(`   📄 Files processed: ${stats.filesProcessed}`);
        console.log(`   💾 Cache size: ${stats.cacheSize} items`);
        console.log(`   🚦 Rate limiter: ${stats.rateLimiter.remaining}/${googleDriveAPI.rateLimiter.maxRequests} requests remaining`);

        console.log('\n🎉 Google Drive API testing completed successfully!');

    } catch (error) {
        console.error('❌ Google Drive API test failed:', error);

        if (error.message.includes('Rate limit')) {
            console.log('💡 Tip: Rate limit exceeded. Wait a moment and try again.');
        } else if (error.message.includes('Insufficient Permission')) {
            console.log('💡 Tip: Check Google Drive API permissions in Google Cloud Console.');
        }
    }
}

/**
 * Performance benchmark
 */
async function benchmarkPerformance() {
    console.log('\n🏃 Running Google Drive API Performance Benchmark...\n');

    const testEmail = process.argv[2];

    if (!testEmail) {
        console.log('⚠️ No test email provided for benchmark');
        console.log('Usage: node test-google-drive-api.js <EMAIL>');
        return;
    }

    try {
        const results = await googleDriveAPI.benchmark(testEmail);

        if (results.error) {
            console.log('❌ Benchmark failed:', results.error);
            return;
        }

        console.log('📊 Benchmark Results:');

        if (results.operations.listFiles) {
            const listFiles = results.operations.listFiles;
            console.log(`\n📁 List Files Performance:`);
            console.log(`   Average: ${listFiles.average.toFixed(2)}ms`);
            console.log(`   Min: ${listFiles.min}ms`);
            console.log(`   Max: ${listFiles.max}ms`);
            console.log(`   All times: ${listFiles.times.join(', ')}ms`);
        }

        if (results.cachePerformance.coldTime) {
            const cache = results.cachePerformance;
            console.log(`\n🔥 Cache Performance:`);
            console.log(`   Cold call: ${cache.coldTime}ms`);
            console.log(`   Warm calls average: ${cache.averageWarmTime.toFixed(2)}ms`);
            console.log(`   Cache speedup: ${cache.speedup.toFixed(1)}x faster`);
        }

        console.log(`\n📊 Final Stats:`);
        console.log(`   Total API calls: ${results.stats.apiCalls}`);
        console.log(`   Cache size: ${results.stats.cacheSize}`);

    } catch (error) {
        console.error('❌ Benchmark failed:', error.message);
    }
}

/**
 * Test specific scenarios
 */
async function testSpecificScenarios() {
    console.log('\n🎯 Testing Specific Migration Scenarios...\n');

    const testEmail = process.argv[2];

    if (!testEmail) {
        console.log('⚠️ No test email provided');
        console.log('Usage: node test-google-drive-api.js <EMAIL>');
        return;
    }

    try {
        // Scenario 1: Find files with complex permissions
        console.log('1. Finding files with complex permissions...');
        const files = await googleDriveAPI.listFiles(testEmail, { pageSize: 20 });

        let complexPermissionFiles = 0;
        for (const file of files.files.slice(0, 5)) {
            const permissions = await googleDriveAPI.getFilePermissions(testEmail, file.id);
            if (permissions.permissions.length > 2) {
                complexPermissionFiles++;
                console.log(`   📄 ${file.name}: ${permissions.permissions.length} permissions`);
            }
        }
        console.log(`   ✅ Found ${complexPermissionFiles} files with complex permissions`);

        // Scenario 2: Identify Google Docs for export
        console.log('\n2. Identifying Google Docs for export...');
        const googleDocsFiles = files.files.filter(file => file.isGoogleDoc);
        console.log(`   📄 Found ${googleDocsFiles.length} Google Docs files`);

        googleDocsFiles.slice(0, 3).forEach(file => {
            const exportType = googleDriveAPI.getExportMimeType(file.mimeType);
            const extension = googleDriveAPI.getFileExtension(exportType);
            console.log(`   📄 ${file.name} → ${exportType} (${extension})`);
        });

        // Scenario 3: Large files analysis
        console.log('\n3. Analyzing large files...');
        const largeFiles = files.files.filter(file => parseInt(file.size) > 10 * 1024 * 1024); // >10MB
        console.log(`   📦 Found ${largeFiles.length} files larger than 10MB`);

        const totalSize = largeFiles.reduce((sum, file) => sum + parseInt(file.size || 0), 0);
        console.log(`   💾 Total size of large files: ${googleDriveAPI.formatFileSize(totalSize)}`);

        console.log('\n✅ Specific scenarios testing completed!');

    } catch (error) {
        console.error('❌ Specific scenarios test failed:', error.message);
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Google Drive API Tests\n');

    await testGoogleDriveAPI();
    await benchmarkPerformance();
    await testSpecificScenarios();

    console.log('\n' + '='.repeat(60));
    console.log('✅ All Google Drive API tests completed!');

    // Show final stats
    const finalStats = googleDriveAPI.getStats();
    console.log(`\n📊 Session Summary:`);
    console.log(`   API calls: ${finalStats.apiCalls}`);
    console.log(`   Errors: ${finalStats.errors}`);
    console.log(`   Files processed: ${finalStats.filesProcessed}`);
    console.log(`   Cache items: ${finalStats.cacheSize}`);

    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testGoogleDriveAPI, benchmarkPerformance, testSpecificScenarios };
