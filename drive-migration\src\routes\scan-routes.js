import express from 'express';
import { driveScanner } from '../services/drive-scanner.js';
import { supabaseClient } from '../database/supabase.js';

const router = express.Router();

/**
 * Start a new Drive scan
 * POST /api/scan/start
 */
router.post('/start', async (req, res) => {
    try {
        const { userEmail, scope, ...options } = req.body;

        if (!userEmail) {
            return res.status(400).json({
                error: 'User email is required'
            });
        }

        console.log(`🔍 Starting scan for ${userEmail} with scope: ${scope}`);

        // Start the scan
        const result = await driveScanner.startFullScan(userEmail, {
            scope,
            ...options
        });

        res.json({
            success: true,
            sessionId: result.sessionId,
            message: '<PERSON>an started successfully'
        });

    } catch (error) {
        console.error('❌ Error starting scan:', error.message);
        res.status(500).json({
            error: 'Failed to start scan',
            details: error.message
        });
    }
});

/**
 * Get scan status
 * GET /api/scan/status/:sessionId
 */
router.get('/status/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        const { data: session, error } = await supabaseClient.getServiceClient()
            .from('scan_sessions')
            .select('*')
            .eq('id', sessionId)
            .single();

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!session) {
            return res.status(404).json({
                error: 'Scan session not found'
            });
        }

        res.json(session);

    } catch (error) {
        console.error('❌ Error getting scan status:', error.message);
        res.status(500).json({
            error: 'Failed to get scan status',
            details: error.message
        });
    }
});

/**
 * Cancel a running scan
 * POST /api/scan/cancel/:sessionId
 */
router.post('/cancel/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        // Stop the scanner
        driveScanner.stopScan();

        // Update session status in database
        const { error } = await supabaseClient.getServiceClient()
            .from('scan_sessions')
            .update({
                status: 'cancelled',
                completed_at: new Date().toISOString()
            })
            .eq('id', sessionId);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            message: 'Scan cancelled successfully'
        });

    } catch (error) {
        console.error('❌ Error cancelling scan:', error.message);
        res.status(500).json({
            error: 'Failed to cancel scan',
            details: error.message
        });
    }
});

/**
 * Get scanned files with pagination and filtering
 * GET /api/scan/files
 */
router.get('/files', async (req, res) => {
    try {
        const {
            sessionId,
            page = 1,
            pageSize = 50,
            search = '',
            mimeType = 'all',
            minSize = '',
            maxSize = '',
            sortBy = 'name',
            sortOrder = 'asc'
        } = req.query;

        if (!sessionId) {
            return res.status(400).json({
                error: 'Session ID is required'
            });
        }

        // Build query
        let query = supabaseClient.getServiceClient()
            .from('scanned_files')
            .select('*', { count: 'exact' })
            .eq('scan_session_id', sessionId);

        // Apply filters
        if (search) {
            query = query.ilike('name', `%${search}%`);
        }

        if (mimeType !== 'all') {
            if (mimeType.endsWith('/')) {
                // Prefix match for categories like 'image/'
                query = query.like('mime_type', `${mimeType}%`);
            } else {
                // Exact match
                query = query.eq('mime_type', mimeType);
            }
        }

        if (minSize) {
            query = query.gte('size', parseInt(minSize));
        }

        if (maxSize) {
            query = query.lte('size', parseInt(maxSize));
        }

        // Apply sorting
        const ascending = sortOrder === 'asc';
        query = query.order(sortBy, { ascending });

        // Apply pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);
        query = query.range(offset, offset + parseInt(pageSize) - 1);

        const { data: files, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            files: files || [],
            totalCount: count || 0,
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            totalPages: Math.ceil((count || 0) / parseInt(pageSize))
        });

    } catch (error) {
        console.error('❌ Error getting scanned files:', error.message);
        res.status(500).json({
            error: 'Failed to get scanned files',
            details: error.message
        });
    }
});

/**
 * Update file selection status
 * POST /api/scan/files/select
 */
router.post('/files/select', async (req, res) => {
    try {
        const { fileIds, isSelected } = req.body;

        if (!Array.isArray(fileIds)) {
            return res.status(400).json({
                error: 'fileIds must be an array'
            });
        }

        const { error } = await supabaseClient.getServiceClient()
            .from('scanned_files')
            .update({ is_selected: isSelected })
            .in('file_id', fileIds);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            message: `Updated selection for ${fileIds.length} files`
        });

    } catch (error) {
        console.error('❌ Error updating file selection:', error.message);
        res.status(500).json({
            error: 'Failed to update file selection',
            details: error.message
        });
    }
});

/**
 * Get scan statistics
 * GET /api/scan/stats/:sessionId
 */
router.get('/stats/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        // Get session info
        const { data: session, error: sessionError } = await supabaseClient.getServiceClient()
            .from('scan_sessions')
            .select('*')
            .eq('id', sessionId)
            .single();

        if (sessionError) {
            throw new Error(`Database error: ${sessionError.message}`);
        }

        // Get file statistics
        const { data: fileStats, error: statsError } = await supabaseClient.getServiceClient()
            .from('scanned_files')
            .select('mime_type, size')
            .eq('scan_session_id', sessionId);

        if (statsError) {
            throw new Error(`Database error: ${statsError.message}`);
        }

        // Calculate statistics
        const stats = {
            totalFiles: fileStats.length,
            totalSize: fileStats.reduce((sum, file) => sum + (file.size || 0), 0),
            fileTypes: {},
            selectedFiles: 0,
            selectedSize: 0
        };

        // Group by file type
        fileStats.forEach(file => {
            const mimeType = file.mime_type;
            if (!stats.fileTypes[mimeType]) {
                stats.fileTypes[mimeType] = { count: 0, size: 0 };
            }
            stats.fileTypes[mimeType].count++;
            stats.fileTypes[mimeType].size += file.size || 0;
        });

        // Get selected files count
        const { count: selectedCount, error: selectedError } = await supabaseClient.getServiceClient()
            .from('scanned_files')
            .select('*', { count: 'exact', head: true })
            .eq('scan_session_id', sessionId)
            .eq('is_selected', true);

        if (!selectedError) {
            stats.selectedFiles = selectedCount || 0;
        }

        res.json({
            session,
            stats
        });

    } catch (error) {
        console.error('❌ Error getting scan statistics:', error.message);
        res.status(500).json({
            error: 'Failed to get scan statistics',
            details: error.message
        });
    }
});

export default router;
