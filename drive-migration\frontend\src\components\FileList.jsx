import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import ErrorDisplay from './ErrorDisplay';

const FileList = ({ scanSession, onFilesSelected, selectedFiles }) => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    mimeType: 'all',
    minSize: '',
    maxSize: '',
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [selectAll, setSelectAll] = useState(false);

  const pageSize = 50;

  useEffect(() => {
    if (scanSession?.id) {
      loadFiles();
    }
  }, [scanSession, currentPage, filters]);

  useEffect(() => {
    // Update select all checkbox based on current selection
    const currentPageFileIds = files.map(f => f.id);
    const selectedOnCurrentPage = selectedFiles.filter(f => currentPageFileIds.includes(f.id));
    setSelectAll(selectedOnCurrentPage.length === files.length && files.length > 0);
  }, [selectedFiles, files]);

  const loadFiles = async () => {
    if (!scanSession?.id) return;

    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        sessionId: scanSession.id,
        page: currentPage,
        pageSize: pageSize,
        ...filters
      });

      const data = await apiGet(`/api/scan/files?${queryParams}`);
      setFiles(data.files || []);
      setTotalPages(Math.ceil((data.totalCount || 0) / pageSize));

    } catch (err) {
      console.error('Error loading files:', err);
      setError(err);
      setFiles([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (file, isSelected) => {
    let newSelection;

    if (isSelected) {
      newSelection = [...selectedFiles, file];
    } else {
      newSelection = selectedFiles.filter(f => f.id !== file.id);
    }

    onFilesSelected(newSelection);
  };

  const handleSelectAll = (isSelected) => {
    let newSelection;

    if (isSelected) {
      // Add all files from current page that aren't already selected
      const currentPageFiles = files.filter(f => !selectedFiles.some(sf => sf.id === f.id));
      newSelection = [...selectedFiles, ...currentPageFiles];
    } else {
      // Remove all files from current page
      const currentPageFileIds = files.map(f => f.id);
      newSelection = selectedFiles.filter(f => !currentPageFileIds.includes(f.id));
    }

    onFilesSelected(newSelection);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const getFileIcon = (mimeType) => {
    if (mimeType === 'application/vnd.google-apps.folder') return '📁';
    if (mimeType === 'application/vnd.google-apps.document') return '📄';
    if (mimeType === 'application/vnd.google-apps.spreadsheet') return '📊';
    if (mimeType === 'application/vnd.google-apps.presentation') return '📽️';
    if (mimeType === 'application/pdf') return '📕';
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    return '📄';
  };

  const isFileSelected = (fileId) => {
    return selectedFiles.some(f => f.id === fileId);
  };

  if (loading) {
    return (
      <div className="file-list">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading files...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="file-list">
        <ErrorDisplay
          error={error}
          title="Lỗi tải danh sách file"
          onRetry={loadFiles}
          onDismiss={() => setError(null)}
        />
      </div>
    );
  }

  return (
    <div className="file-list">
      <h2>📋 Select Files to Migrate</h2>

      <div className="file-filters">
        <div className="filter-row">
          <input
            type="text"
            placeholder="Search files..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="search-input"
          />

          <select
            value={filters.mimeType}
            onChange={(e) => handleFilterChange('mimeType', e.target.value)}
            className="filter-select"
          >
            <option value="all">All File Types</option>
            <option value="application/vnd.google-apps.document">Google Docs</option>
            <option value="application/vnd.google-apps.spreadsheet">Google Sheets</option>
            <option value="application/vnd.google-apps.presentation">Google Slides</option>
            <option value="application/pdf">PDF Files</option>
            <option value="image/">Images</option>
            <option value="application/vnd.google-apps.folder">Folders</option>
          </select>

          <select
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
            className="filter-select"
          >
            <option value="name">Sort by Name</option>
            <option value="size">Sort by Size</option>
            <option value="modified_time">Sort by Modified</option>
            <option value="created_time">Sort by Created</option>
          </select>

          <select
            value={filters.sortOrder}
            onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
            className="filter-select"
          >
            <option value="asc">Ascending</option>
            <option value="desc">Descending</option>
          </select>
        </div>
      </div>

      <div className="selection-summary">
        <div className="selection-info">
          <span>Selected: <strong>{selectedFiles.length}</strong> files</span>
          <span>Total Size: <strong>{formatFileSize(selectedFiles.reduce((sum, f) => sum + (f.size || 0), 0))}</strong></span>
        </div>

        <label className="select-all-checkbox">
          <input
            type="checkbox"
            checked={selectAll}
            onChange={(e) => handleSelectAll(e.target.checked)}
          />
          <span className="checkbox-custom"></span>
          Select All on Page
        </label>
      </div>

      <div className="files-table">
        <div className="table-header">
          <div className="col-select">Select</div>
          <div className="col-icon">Type</div>
          <div className="col-name">Name</div>
          <div className="col-size">Size</div>
          <div className="col-modified">Modified</div>
          <div className="col-path">Path</div>
        </div>

        <div className="table-body">
          {files.map((file) => (
            <div key={file.id} className="table-row">
              <div className="col-select">
                <label className="file-checkbox">
                  <input
                    type="checkbox"
                    checked={isFileSelected(file.id)}
                    onChange={(e) => handleFileSelect(file, e.target.checked)}
                  />
                  <span className="checkbox-custom"></span>
                </label>
              </div>

              <div className="col-icon">
                <span className="file-icon">{getFileIcon(file.mime_type)}</span>
              </div>

              <div className="col-name">
                <span className="file-name" title={file.name}>
                  {file.name}
                </span>
              </div>

              <div className="col-size">
                {formatFileSize(file.size)}
              </div>

              <div className="col-modified">
                {file.modified_time ? new Date(file.modified_time).toLocaleDateString() : 'N/A'}
              </div>

              <div className="col-path">
                <span className="file-path" title={file.full_path}>
                  {file.full_path}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="pagination">
        <button
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="btn btn-secondary btn-small"
        >
          ← Previous
        </button>

        <span className="page-info">
          Page {currentPage} of {totalPages}
        </span>

        <button
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="btn btn-secondary btn-small"
        >
          Next →
        </button>
      </div>

      <div className="action-buttons">
        <button
          onClick={() => onFilesSelected(selectedFiles)}
          disabled={selectedFiles.length === 0}
          className="btn btn-primary btn-large"
        >
          Continue with {selectedFiles.length} Selected Files
        </button>
      </div>
    </div>
  );
};

export default FileList;
