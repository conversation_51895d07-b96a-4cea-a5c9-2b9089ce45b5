# Tài liệu Yêu Cầu Ứng Dụng "Drive-to-Lark Migrator"

## 1. <PERSON><PERSON><PERSON> đích và Phạm vi
Ứng dụng **Drive-to-Lark Migrator** hỗ trợ tổ chức di chuyển (migrate) dữ liệu từ Google Drive sang Lark Drive một cách an toàn, giữ nguyên cấu trúc thư mục và quyền chia sẻ[1][4][23]. Ứng dụng nhắm tới quy mô **toàn miền** Google Workspace với khả năng chạy nhiều tác vụ song song, giám sát tiến trình theo thời gian thực và tạo báo cáo sau di chuyển.

## 2. <PERSON><PERSON><PERSON> tượng sử dụng
* **IT Admin (Google & Lark)**: cấu hình tài khoản dịch vụ, mapping người dùng, giám sát tiến trình.
* **Nhân viên bình thường**: chỉ xem báo cáo hoặc nhận quyền truy cập vào file đã di chuyển.

## 3. <PERSON><PERSON><PERSON> năng cấp cao
| Mã  | Chức năng                | Mô tả                                                                                                              |
| --- | ------------------------ | ------------------------------------------------------------------------------------------------------------------ |
| F1  | Xác thực & Ủy quyền      | Đăng nhập bằng Admin Google (Service Account + Domain-wide Delegation) và Admin Lark (Tenant Access Token)[1][9].  |
| F2  | Chọn phạm vi di chuyển   | Toàn bộ My Drive của người dùng hoặc một thư mục theo đường dẫn nhập (bao gồm thư mục con)[35][37].                |
| F3  | Mapping & bảo toàn quyền | Tự động ánh xạ email Google → Lark thông qua bảng người dùng; hiển thị giao diện chỉnh tay nếu không khớp[41][55]. |
| F4  | Thực thi di chuyển       | Tải file từ Drive (`files.get` + `alt=media`) và tải lên Lark (`drive/v1/upload_all`)[23][6].                      |
| F5  | Theo dõi tiến trình      | Giao diện realtime (% hoàn thành, tốc độ, lỗi từng file) qua Supabase Realtime[64][72].                            |
| F6  | Lịch sử & Báo cáo        | Lưu log vào bảng `migrations`, xuất CSV/PDF, hiển thị dashboard[61][62].                                           |

## 4. User Stories
1. **ADM-01**: Là admin, tôi muốn đăng nhập bằng khóa JSON của Service Account để ứng dụng giả lập người dùng và liệt kê file.
2. **ADM-02**: Là admin, tôi muốn nhập đường dẫn `/Shared/Projects/Q1` để chỉ di chuyển thư mục đó.
3. **ADM-03**: Là admin, tôi muốn xem bảng mapping và chỉnh sửa những email chưa khớp để đảm bảo quyền chia sẻ được bảo toàn.
4. **ADM-04**: Là admin, tôi muốn nhận thông báo realtime nếu file nào lỗi và lý do (quota, dung lượng, API 429).
5. **ADM-05**: Là admin, tôi muốn tải xuống báo cáo CSV kết quả di chuyển (tên file, kích thước, trạng thái, thời gian).

## 5. Luồng tác nghiệp chính
```mermaid
flowchart TD
    A[Đăng nhập] --> B{Chọn phạm vi}
    B -->|Toàn bộ| C[Liệt kê tất cả file]
    B -->|Thư mục| D[Tra cứu ID thư mục]
    C --> E[Mapping quyền]
    D --> E
    E --> F[Thực thi di chuyển]
    F --> G[Theo dõi realtime]
    G --> H[Hoàn tất & sinh báo cáo]
```

## 6. Yêu cầu chức năng chi tiết
### 6.1 Xác thực
* Ứng dụng đọc file **JSON key** của Service Account và sinh JWT với claim `sub=<EMAIL>` để giả lập từng user[1][23].
* Gọi `https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal` để lấy **Tenant Access Token**; cache 2h[9].

### 6.2 Lựa chọn & Quét dữ liệu
* Nếu chọn toàn bộ, gọi Drive API `files.list` với `q='trashed=false'` và `supportsAllDrives=true` kèm `useDomainAdminAccess=true`[26][34].
* Nếu chọn thư mục, resolve đường dẫn → folderId bằng BFS; giới hạn độ sâu 100 thư mục theo quota Drive[25].

### 6.3 Tải và Upload
* File nhị phân: `files.get?alt=media` → stream.
* Google Docs: `files.export` sang MIME tương đương (`application/vnd.openxmlformats-officedocument.wordprocessingml.document`)[23].
* Lark upload theo quy trình **multipart**: `drive/v1/upload_all` (max 5 QPS, 300 MB/chunk)[6].
* Tạo thư mục trên Lark trước bằng API `drive/v1/folder/create` khi chưa tồn tại.

### 6.4 Quyền chia sẻ
* Truy xuất `permissions.list` cho mỗi file, lấy `emailAddress`, `role`[34][38].
* Map `emailAddress` → `user_id` (Lark) thông qua bảng `users`; nếu không khớp, thêm vào danh sách pending mapping để admin xử lý[59].
* Gọi `drive/v1/permission-public/patch` để gán quyền trên file Lark; hỗ trợ roles viewer / editor[4].

### 6.5 Theo dõi & Lịch sử
* Supabase bảng `migration_tasks` (id, user, scope, status, progress, started_at, finished_at).
* Sử dụng **Supabase Realtime Broadcast** để push cập nhật UI[64][72].
* Log chi tiết từng file vào bảng `migration_items`; lưu mã lỗi Google/Lark, retry count, thời gian.

## 7. Yêu cầu Phi chức năng
| Thuộc tính | Mục tiêu                                                                                  |
| ---------- | ----------------------------------------------------------------------------------------- |
| Hiệu năng  | 500 tệp/phút với tệp < 50 MB; tự động exponential back-off khi nhận 429/403 từ Drive[32]. |
| Bảo mật    | Lưu khóa JSON & token trong biến môi trường; mã hoá ở trạng thái nghỉ (Supabase Vault).   |
| Khả dụng   | Khôi phục tác vụ đang chạy sau khi server restart; lưu checkpoint trong DB.               |
| Giới hạn   | Tôn trọng hạn mức 25 000 queries/100 s của Drive API[24].                                 |

## 8. Kiến trúc Đề xuất
![arch](architecture.png)
* **Frontend (React 18 + Vite)**: quản lý trạng thái qua React Query, hiển thị tiến trình realtime qua websockets.
* **Backend (Supabase)**:
  * **PostgreSQL**: bảng `users`, `migration_tasks`, `migration_items`.
  * **Edge Functions**: endpoints `/start`, `/status`, `/report` viết bằng TypeScript, chạy tách biệt khỏi UI.
  * **Storage**: lưu tạm file trung gian (> 500 MB) trước khi upload Lark (xoá sau 24 h).
* **Worker Node.js** (Supabase Function) thực thi di chuyển không chặn giao diện.

## 9. API nội bộ
| Method | Endpoint          | Mô tả                                       |
| ------ | ----------------- | ------------------------------------------- |
| POST   | /tasks            | Khởi tạo tác vụ migrate, body `{scope:"all" | "path", path?:string}` |
| GET    | /tasks/:id        | Trả JSON tiến trình (%, speed, eta)         |
| GET    | /tasks/:id/report | Trả về link CSV/PDF báo cáo                 |

## 10. Thiết kế CSDL (PostgreSQL)
```sql
-- bảng mapping người dùng
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email_google TEXT UNIQUE,
  lark_userid TEXT,
  mapped BOOLEAN DEFAULT FALSE
);
-- bảng tác vụ
CREATE TABLE migration_tasks (
  id UUID PRIMARY KEY,
  owner_email TEXT,
  scope TEXT,
  path TEXT,
  status TEXT,
  progress NUMERIC,
  started_at TIMESTAMPTZ,
  finished_at TIMESTAMPTZ
);
-- bảng tệp
CREATE TABLE migration_items (
  id UUID PRIMARY KEY,
  task_id UUID REFERENCES migration_tasks(id),
  src_file_id TEXT,
  dest_file_id TEXT,
  size BIGINT,
  status TEXT,
  error TEXT,
  retries INT DEFAULT 0,
  migrated_at TIMESTAMPTZ
);
```

## 11. Mapping Logic
1. Đọc bảng `users` để tạo Map<String, String>.
2. Khi gặp `permission` lạ, insert bản ghi `mapped=false` và thêm vào hàng đợi UI.
3. UI cho phép admin sửa `lark_userid`, cập nhật `mapped=true`, worker retry gắn quyền.

## 12. Rủi ro & Giải pháp
| Rủi ro                             | Biện pháp                                                              |
| ---------------------------------- | ---------------------------------------------------------------------- |
| Quota Drive/Lark                   | Throttle, exponential back-off, hiển thị hàng đợi chờ[24][6].          |
| File > 15 GB                       | Lark giới hạn 15 GB/tệp; đánh dấu lỗi và ghi chú để xử lý thủ công[6]. |
| Google Docs đặc biệt (Sites, Maps) | Bỏ qua hoặc xuất HTML snapshot; log cảnh báo.                          |

## 13. Lộ trình triển khai
| Sprint | Hạng mục                                  | Thời gian |
| ------ | ----------------------------------------- | --------- |
| 0      | Thiết lập Supabase, CI/CD, repo           | 1 tuần    |
| 1      | Đăng nhập, lưu khóa, test API Google/Lark | 2 tuần    |
| 2      | Quét Drive, lựa chọn phạm vi              | 2 tuần    |
| 3      | Upload Lark, mapping quyền                | 3 tuần    |
| 4      | Realtime progress & UI                    | 2 tuần    |
| 5      | Báo cáo, tải xuống, hardening             | 2 tuần    |
| 6      | UAT & Go-live                             | 1 tuần    |

## 14. Phụ lục
### 14.1 OAuth Scopes
* Google: `drive.readonly`, `drive.metadata.readonly`, `admin.directory.user.readonly`[5].
* Lark: `contact:user:readonly`, `drive:space:write`, `drive:file:write`, `drive:permission:write`[4][57].

### 14.2 Giới hạn API
* Drive: 25 000 request/100 s/user, 750 GB/ngày tải xuống[32].
* Lark Upload: 5 QPS / token, 300 MB chunk, 15 GB max file[6].

---
**Tài liệu này hoàn thành ngày 13-07-2025.**