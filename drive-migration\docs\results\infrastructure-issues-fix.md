# Infrastructure Issues & Fixes

## Trạng thái Infrastructure Check

### ✅ Working Components
- **Environment Variables**: All 11 required variables set
- **Supabase Database**: All 5 tables created and accessible
- **Google Authentication**: Service Account credentials valid
- **Lark Authentication**: App credentials valid, token acquisition working

### ❌ Issues Detected

#### 1. Google Drive API - `invalid_grant: account not found`

**Vấn đề**: Service Account không thể impersonate user email `<EMAIL>`

**Nguyên nhân có thể**:
- Domain-wide delegation chưa được enable trong Google Admin Console
- Service Account chưa được authorize với proper scopes
- Email test không thuộc domain được manage

**Cách fix**:

1. **Kiểm tra Google Admin Console**:
   - Đăng nhập vào [Google Admin Console](https://admin.google.com)
   - Vào Security > API Controls > Domain-wide Delegation
   - Tìm Client ID: `107712834684081862210`
   - <PERSON><PERSON><PERSON> chưa có, thêm với scopes:
     ```
     https://www.googleapis.com/auth/drive
     https://www.googleapis.com/auth/drive.file
     https://www.googleapis.com/auth/drive.metadata
     https://www.googleapis.com/auth/admin.directory.user.readonly
     ```

2. **Kiểm tra Service Account**:
   - Vào [Google Cloud Console](https://console.cloud.google.com)
   - IAM & Admin > Service Accounts
   - Tìm `<EMAIL>`
   - Đảm bảo "Enable Google Workspace Domain-wide Delegation" được check

3. **Kiểm tra domain**:
   - Email test `<EMAIL>` phải thuộc domain được manage trong Google Workspace
   - Hoặc thử với email khác trong domain

#### 2. Lark Drive API - `field validation failed`

**Vấn đề**: API call tạo folder bị lỗi validation

**Nguyên nhân có thể**:
- Lark App chưa có permission tạo folder
- API call format không đúng
- Tenant không có Lark Drive enabled

**Cách fix**:

1. **Kiểm tra Lark App Permissions**:
   - Đăng nhập vào [Lark Developer Console](https://open.larksuite.com/app)
   - Tìm App ID: `cli_a8f976ac18b5d02f`
   - Vào Permissions & Scopes
   - Đảm bảo có các permissions:
     ```
     drive:drive
     drive:drive.readonly
     drive:drive.file
     ```

2. **Kiểm tra Lark Drive Status**:
   - Đảm bảo tenant có Lark Drive được enable
   - Kiểm tra trong Lark Admin Console

3. **Test với API đơn giản hơn**:
   - Thử list files trước khi tạo folder
   - Kiểm tra API response format

## Temporary Workarounds

### Google Drive API
```javascript
// Thử với email khác hoặc skip Google API test tạm thời
const testEmail = process.env.GOOGLE_TEST_EMAIL || '<EMAIL>';
```

### Lark Drive API  
```javascript
// Test với read-only operations trước
const { data, error } = await larkDriveAPI.listFiles();
```

## Next Steps

1. **Ưu tiên cao**: Fix Google Domain-wide Delegation
2. **Ưu tiên trung bình**: Fix Lark Drive permissions
3. **Backup plan**: Implement manual credential input trong UI

## Infrastructure Status Summary

```
✅ Environment Variables    (11/11 working)
✅ Supabase Database       (5/5 tables working)  
✅ Google Authentication   (credentials valid)
✅ Lark Authentication     (token acquisition working)
❌ Google Drive API        (domain delegation issue)
❌ Lark Drive API          (permission/validation issue)
```

**Overall**: 4/6 components working (67% ready)

## Impact on Development

- **Sprint 2 can proceed** với database và authentication foundation
- **Google/Lark API integration** cần fix trước khi test end-to-end
- **UI development** có thể tiếp tục với mock data

---
*Documented: 2025-07-13*
